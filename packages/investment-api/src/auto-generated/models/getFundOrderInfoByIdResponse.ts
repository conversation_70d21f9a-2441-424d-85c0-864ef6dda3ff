/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { FundOrderTypeEnum } from "./fundOrderTypeEnum";
import type { GetFundOrderInfoByIdResponsePaymentType } from "./getFundOrderInfoByIdResponsePaymentType";
import type { GetFundOrderInfoByIdResponseFundOrderIssuanceByBankReceipt } from "./getFundOrderInfoByIdResponseFundOrderIssuanceByBankReceipt";
import type { GetFundOrderInfoByIdResponseFundOrderIssuanceByIPG } from "./getFundOrderInfoByIdResponseFundOrderIssuanceByIPG";
import type { GetFundOrderInfoByIdResponseFundOrderRevoke } from "./getFundOrderInfoByIdResponseFundOrderRevoke";

export interface GetFundOrderInfoByIdResponse {
  id?: string;
  createDate?: string;
  fundId?: string;
  fundName?: string;
  fundOrderType?: FundOrderTypeEnum;
  /** @nullable */
  paymentType?: GetFundOrderInfoByIdResponsePaymentType;
  /** @nullable */
  fundOrderIssuanceByBankReceipt?: GetFundOrderInfoByIdResponseFundOrderIssuanceByBankReceipt;
  /** @nullable */
  fundOrderIssuanceByIPG?: GetFundOrderInfoByIdResponseFundOrderIssuanceByIPG;
  /** @nullable */
  fundOrderRevoke?: GetFundOrderInfoByIdResponseFundOrderRevoke;
}
