/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { ApiResultOfPagingOutputOfGetInvestorStatementsResponseData } from "./apiResultOfPagingOutputOfGetInvestorStatementsResponseData";

export interface ApiResultOfPagingOutputOfGetInvestorStatementsResponse {
  /** @nullable */
  data?: ApiResultOfPagingOutputOfGetInvestorStatementsResponseData;
  isSuccess?: boolean;
  /** @nullable */
  errorCode?: string | null;
  /** @nullable */
  errorMessage?: string | null;
}
