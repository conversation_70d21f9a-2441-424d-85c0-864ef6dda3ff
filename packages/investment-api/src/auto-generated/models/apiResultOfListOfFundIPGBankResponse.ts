/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { FundIPGBankResponse } from "./fundIPGBankResponse";

export interface ApiResultOfListOfFundIPGBankResponse {
  /** @nullable */
  data?: FundIPGBankResponse[] | null;
  isSuccess?: boolean;
  /** @nullable */
  errorCode?: string | null;
  /** @nullable */
  errorMessage?: string | null;
}
