/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { UserSejamBankAccountResponse } from "./userSejamBankAccountResponse";

export interface UserSejamProfileResponse {
  id?: string;
  createDate?: string;
  userId?: string;
  financialInstituteId?: string;
  /** @nullable */
  rayanCustomerId?: number | null;
  bourseCode?: string;
  nationalCode?: string;
  firstName?: string;
  lastName?: string;
  fatherName?: string;
  /** @nullable */
  birthDate?: string | null;
  birthPlace?: string;
  birthCertNumber?: string;
  birthCertificationId?: string;
  phoneNumber?: string;
  mobileNumber?: string;
  /** @nullable */
  email?: string | null;
  address?: string;
  postalCode?: string;
  bankAccounts?: UserSejamBankAccountResponse[];
}
