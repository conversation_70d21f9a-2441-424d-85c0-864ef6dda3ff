/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { FinancialInstituteTypeEnum } from "./financialInstituteTypeEnum";
import type { FinancialInstituteInfoResponseFundConfig } from "./financialInstituteInfoResponseFundConfig";

export interface FinancialInstituteInfoResponse {
  id?: string;
  symbolName?: string;
  fullName?: string;
  /** @nullable */
  logo?: string | null;
  financialInstituteType?: FinancialInstituteTypeEnum;
  /** @nullable */
  fundConfig?: FinancialInstituteInfoResponseFundConfig;
}
