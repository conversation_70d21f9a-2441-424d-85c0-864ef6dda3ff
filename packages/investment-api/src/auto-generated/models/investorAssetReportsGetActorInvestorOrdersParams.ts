/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { FundOrderTypeEnum } from "./fundOrderTypeEnum";

export type InvestorAssetReportsGetActorInvestorOrdersParams = {
  FundOrderType?: FundOrderTypeEnum | null;
  FromDate?: string | null;
  ToDate?: string | null;
  FundId?: string;
  PageNumber?: number;
  PageSize?: number;
};
