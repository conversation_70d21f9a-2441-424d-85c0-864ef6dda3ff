/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { FundAssetCompositionTypeEnum } from "./fundAssetCompositionTypeEnum";

export interface FundAssetCompositionViewModel {
  assetCompositionType?: FundAssetCompositionTypeEnum;
  title?: string;
  assetCompositionPercent?: number;
  assetCompositionValue?: number;
  viewIndex?: number;
  reportDate?: string;
}
