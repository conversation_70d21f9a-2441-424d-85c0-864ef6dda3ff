/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { UserSejamProfileResponse } from "./userSejamProfileResponse";

export interface UserProfileResponse {
  userId?: string;
  createDate?: string;
  isActive?: boolean;
  userName?: string;
  mobileNumber?: string;
  /** @nullable */
  firstName?: string | null;
  /** @nullable */
  lastName?: string | null;
  /** @nullable */
  nationalCode?: string | null;
  /** @nullable */
  bourseCode?: string | null;
  sejamProfiles?: UserSejamProfileResponse[];
}
