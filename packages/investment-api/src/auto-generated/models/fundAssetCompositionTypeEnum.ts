/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */

export type FundAssetCompositionTypeEnum =
  (typeof FundAssetCompositionTypeEnum)[keyof typeof FundAssetCompositionTypeEnum];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const FundAssetCompositionTypeEnum = {
  None: 0,
  Stock: 100,
  Bond: 200,
  Deposit: 300,
  GoodsDepositCertificate: 400,
  Fund: 600,
  Other: 700,
  Cash: 800,
} as const;
