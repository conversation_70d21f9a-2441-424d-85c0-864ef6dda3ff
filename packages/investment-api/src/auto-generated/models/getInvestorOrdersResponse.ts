/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { FundOrderStatusEnum } from "./fundOrderStatusEnum";
import type { FundOrderTypeEnum } from "./fundOrderTypeEnum";

export interface GetInvestorOrdersResponse {
  /** @nullable */
  rayanFundOrderId?: number | null;
  /** @nullable */
  unitPrice?: number | null;
  /** @nullable */
  orderAmount?: number | null;
  /** @nullable */
  rayanFoStatusId?: number | null;
  fundOrderStatus?: FundOrderStatusEnum;
  /** @nullable */
  fundOrderStatusName?: string | null;
  /** @nullable */
  rayanFundOrderNumber?: string | null;
  /** @nullable */
  fundUnit?: number | null;
  /** @nullable */
  orderDate?: string | null;
  /** @nullable */
  creationDate?: string | null;
  /** @nullable */
  creationTime?: string | null;
  /** @nullable */
  modificationDate?: string | null;
  /** @nullable */
  modificationTime?: string | null;
  /** @nullable */
  licenseDate?: string | null;
  /** @nullable */
  orderPaymentTypeName?: string | null;
  /** @nullable */
  receiptNumber?: string | null;
  /** @nullable */
  receiptDate?: string | null;
  /** @nullable */
  fundName?: string | null;
  isPurchase?: boolean;
  fundOrderType?: FundOrderTypeEnum;
  fundOrderTypeName?: string;
  /** @nullable */
  baseOrderDate?: string | null;
  /** @nullable */
  licenseNumber?: string | null;
}
