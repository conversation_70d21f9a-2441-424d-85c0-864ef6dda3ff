/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { ApiResultOfGetFundNetAssetValueWithChangesResponseData } from "./apiResultOfGetFundNetAssetValueWithChangesResponseData";

export interface ApiResultOfGetFundNetAssetValueWithChangesResponse {
  /** @nullable */
  data?: ApiResultOfGetFundNetAssetValueWithChangesResponseData;
  isSuccess?: boolean;
  /** @nullable */
  errorCode?: string | null;
  /** @nullable */
  errorMessage?: string | null;
}
