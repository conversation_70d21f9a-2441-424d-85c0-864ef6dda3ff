/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { UserRoleProfileResponse } from "./userRoleProfileResponse";
import type { GetLoggedInUserProfileResponseUserProfile } from "./getLoggedInUserProfileResponseUserProfile";
import type { GetLoggedInUserProfileResponseVisitorUserProfile } from "./getLoggedInUserProfileResponseVisitorUserProfile";

export interface GetLoggedInUserProfileResponse {
  roles?: UserRoleProfileResponse[];
  /** @nullable */
  userProfile?: GetLoggedInUserProfileResponseUserProfile;
  /** @nullable */
  visitorUserProfile?: GetLoggedInUserProfileResponseVisitorUserProfile;
}
