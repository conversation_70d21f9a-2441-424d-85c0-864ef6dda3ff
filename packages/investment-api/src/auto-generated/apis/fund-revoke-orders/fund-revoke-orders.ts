/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useMutation } from "@tanstack/react-query";
import type {
  MutationFunction,
  QueryClient,
  UseMutationOptions,
  UseMutationResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfBoolean,
  ApiResultOfNullableGuid,
  CreateRevokeOrderCommand,
  SendRevokeOrderOTPCommand,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary ارسال کد یکبار مصرف درخواست ابطال
 */
export const fundRevokeOrdersSendRevokeOrderOTP = (
  sendRevokeOrderOTPCommand: SendRevokeOrderOTPCommand,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfBoolean>(
    {
      url: `/api/fundcustomer/v1/FundRevokeOrders/SendRevokeOrderOTP`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: sendRevokeOrderOTPCommand,
      signal,
    },
    options,
  );
};

export const getFundRevokeOrdersSendRevokeOrderOTPMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof fundRevokeOrdersSendRevokeOrderOTP>>,
    TError,
    { data: SendRevokeOrderOTPCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof fundRevokeOrdersSendRevokeOrderOTP>>,
  TError,
  { data: SendRevokeOrderOTPCommand },
  TContext
> => {
  const mutationKey = ["fundRevokeOrdersSendRevokeOrderOTP"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof fundRevokeOrdersSendRevokeOrderOTP>>,
    { data: SendRevokeOrderOTPCommand }
  > = (props) => {
    const { data } = props ?? {};

    return fundRevokeOrdersSendRevokeOrderOTP(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type FundRevokeOrdersSendRevokeOrderOTPMutationResult = NonNullable<
  Awaited<ReturnType<typeof fundRevokeOrdersSendRevokeOrderOTP>>
>;
export type FundRevokeOrdersSendRevokeOrderOTPMutationBody =
  SendRevokeOrderOTPCommand;
export type FundRevokeOrdersSendRevokeOrderOTPMutationError =
  ErrorType<unknown>;

/**
 * @summary ارسال کد یکبار مصرف درخواست ابطال
 */
export const useFundRevokeOrdersSendRevokeOrderOTP = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof fundRevokeOrdersSendRevokeOrderOTP>>,
      TError,
      { data: SendRevokeOrderOTPCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof fundRevokeOrdersSendRevokeOrderOTP>>,
  TError,
  { data: SendRevokeOrderOTPCommand },
  TContext
> => {
  const mutationOptions =
    getFundRevokeOrdersSendRevokeOrderOTPMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary ثبت درخواست ابطال
 */
export const fundRevokeOrdersCreateRevokeOrder = (
  createRevokeOrderCommand: CreateRevokeOrderCommand,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfNullableGuid>(
    {
      url: `/api/fundcustomer/v1/FundRevokeOrders/CreateRevokeOrder`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: createRevokeOrderCommand,
      signal,
    },
    options,
  );
};

export const getFundRevokeOrdersCreateRevokeOrderMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof fundRevokeOrdersCreateRevokeOrder>>,
    TError,
    { data: CreateRevokeOrderCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof fundRevokeOrdersCreateRevokeOrder>>,
  TError,
  { data: CreateRevokeOrderCommand },
  TContext
> => {
  const mutationKey = ["fundRevokeOrdersCreateRevokeOrder"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof fundRevokeOrdersCreateRevokeOrder>>,
    { data: CreateRevokeOrderCommand }
  > = (props) => {
    const { data } = props ?? {};

    return fundRevokeOrdersCreateRevokeOrder(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type FundRevokeOrdersCreateRevokeOrderMutationResult = NonNullable<
  Awaited<ReturnType<typeof fundRevokeOrdersCreateRevokeOrder>>
>;
export type FundRevokeOrdersCreateRevokeOrderMutationBody =
  CreateRevokeOrderCommand;
export type FundRevokeOrdersCreateRevokeOrderMutationError = ErrorType<unknown>;

/**
 * @summary ثبت درخواست ابطال
 */
export const useFundRevokeOrdersCreateRevokeOrder = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof fundRevokeOrdersCreateRevokeOrder>>,
      TError,
      { data: CreateRevokeOrderCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof fundRevokeOrdersCreateRevokeOrder>>,
  TError,
  { data: CreateRevokeOrderCommand },
  TContext
> => {
  const mutationOptions =
    getFundRevokeOrdersCreateRevokeOrderMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
