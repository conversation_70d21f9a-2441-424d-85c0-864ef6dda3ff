/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfGetFundNetAssetValueWithChangesResponse,
  ApiResultOfGetFundNetAssetValuesChartResponse,
  ApiResultOfGetFundTotalNetAssetValueResponse,
  NetAssetValueReportsGetFundNetAssetValueWithChangesParams,
  NetAssetValueReportsGetFundNetAssetValuesChartParams,
  NetAssetValueReportsGetFundTotalNetAssetValueParams,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary دریافت خالص ارزش دارایی صندوق
 */
export const netAssetValueReportsGetFundTotalNetAssetValue = (
  params?: NetAssetValueReportsGetFundTotalNetAssetValueParams,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfGetFundTotalNetAssetValueResponse>(
    {
      url: `/api/general/v1/NetAssetValueReports/GetFundTotalNetAssetValue`,
      method: "GET",
      params,
      signal,
    },
    options,
  );
};

export const getNetAssetValueReportsGetFundTotalNetAssetValueQueryKey = (
  params?: NetAssetValueReportsGetFundTotalNetAssetValueParams,
) => {
  return [
    `/api/general/v1/NetAssetValueReports/GetFundTotalNetAssetValue`,
    ...(params ? [params] : []),
  ] as const;
};

export const getNetAssetValueReportsGetFundTotalNetAssetValueQueryOptions = <
  TData = Awaited<
    ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>
  >,
  TError = ErrorType<unknown>,
>(
  params?: NetAssetValueReportsGetFundTotalNetAssetValueParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getNetAssetValueReportsGetFundTotalNetAssetValueQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>>
  > = ({ signal }) =>
    netAssetValueReportsGetFundTotalNetAssetValue(
      params,
      requestOptions,
      signal,
    );

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type NetAssetValueReportsGetFundTotalNetAssetValueQueryResult =
  NonNullable<
    Awaited<ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>>
  >;
export type NetAssetValueReportsGetFundTotalNetAssetValueQueryError =
  ErrorType<unknown>;

export function useNetAssetValueReportsGetFundTotalNetAssetValue<
  TData = Awaited<
    ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>
  >,
  TError = ErrorType<unknown>,
>(
  params: undefined | NetAssetValueReportsGetFundTotalNetAssetValueParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>
        >,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>
          >,
          TError,
          Awaited<
            ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useNetAssetValueReportsGetFundTotalNetAssetValue<
  TData = Awaited<
    ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>
  >,
  TError = ErrorType<unknown>,
>(
  params?: NetAssetValueReportsGetFundTotalNetAssetValueParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>
        >,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>
          >,
          TError,
          Awaited<
            ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useNetAssetValueReportsGetFundTotalNetAssetValue<
  TData = Awaited<
    ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>
  >,
  TError = ErrorType<unknown>,
>(
  params?: NetAssetValueReportsGetFundTotalNetAssetValueParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دریافت خالص ارزش دارایی صندوق
 */

export function useNetAssetValueReportsGetFundTotalNetAssetValue<
  TData = Awaited<
    ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>
  >,
  TError = ErrorType<unknown>,
>(
  params?: NetAssetValueReportsGetFundTotalNetAssetValueParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof netAssetValueReportsGetFundTotalNetAssetValue>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions =
    getNetAssetValueReportsGetFundTotalNetAssetValueQueryOptions(
      params,
      options,
    );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary دریافت قیمت صدور و ابطال بعلاوه تغییرات
 */
export const netAssetValueReportsGetFundNetAssetValueWithChanges = (
  params?: NetAssetValueReportsGetFundNetAssetValueWithChangesParams,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfGetFundNetAssetValueWithChangesResponse>(
    {
      url: `/api/general/v1/NetAssetValueReports/GetFundNetAssetValueWithChanges`,
      method: "GET",
      params,
      signal,
    },
    options,
  );
};

export const getNetAssetValueReportsGetFundNetAssetValueWithChangesQueryKey = (
  params?: NetAssetValueReportsGetFundNetAssetValueWithChangesParams,
) => {
  return [
    `/api/general/v1/NetAssetValueReports/GetFundNetAssetValueWithChanges`,
    ...(params ? [params] : []),
  ] as const;
};

export const getNetAssetValueReportsGetFundNetAssetValueWithChangesQueryOptions =
  <
    TData = Awaited<
      ReturnType<typeof netAssetValueReportsGetFundNetAssetValueWithChanges>
    >,
    TError = ErrorType<unknown>,
  >(
    params?: NetAssetValueReportsGetFundNetAssetValueWithChangesParams,
    options?: {
      query?: Partial<
        UseQueryOptions<
          Awaited<
            ReturnType<
              typeof netAssetValueReportsGetFundNetAssetValueWithChanges
            >
          >,
          TError,
          TData
        >
      >;
      request?: SecondParameter<typeof api>;
    },
  ) => {
    const { query: queryOptions, request: requestOptions } = options ?? {};

    const queryKey =
      queryOptions?.queryKey ??
      getNetAssetValueReportsGetFundNetAssetValueWithChangesQueryKey(params);

    const queryFn: QueryFunction<
      Awaited<
        ReturnType<typeof netAssetValueReportsGetFundNetAssetValueWithChanges>
      >
    > = ({ signal }) =>
      netAssetValueReportsGetFundNetAssetValueWithChanges(
        params,
        requestOptions,
        signal,
      );

    return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
      Awaited<
        ReturnType<typeof netAssetValueReportsGetFundNetAssetValueWithChanges>
      >,
      TError,
      TData
    > & { queryKey: DataTag<QueryKey, TData, TError> };
  };

export type NetAssetValueReportsGetFundNetAssetValueWithChangesQueryResult =
  NonNullable<
    Awaited<
      ReturnType<typeof netAssetValueReportsGetFundNetAssetValueWithChanges>
    >
  >;
export type NetAssetValueReportsGetFundNetAssetValueWithChangesQueryError =
  ErrorType<unknown>;

export function useNetAssetValueReportsGetFundNetAssetValueWithChanges<
  TData = Awaited<
    ReturnType<typeof netAssetValueReportsGetFundNetAssetValueWithChanges>
  >,
  TError = ErrorType<unknown>,
>(
  params: undefined | NetAssetValueReportsGetFundNetAssetValueWithChangesParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof netAssetValueReportsGetFundNetAssetValueWithChanges>
        >,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<
            ReturnType<
              typeof netAssetValueReportsGetFundNetAssetValueWithChanges
            >
          >,
          TError,
          Awaited<
            ReturnType<
              typeof netAssetValueReportsGetFundNetAssetValueWithChanges
            >
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useNetAssetValueReportsGetFundNetAssetValueWithChanges<
  TData = Awaited<
    ReturnType<typeof netAssetValueReportsGetFundNetAssetValueWithChanges>
  >,
  TError = ErrorType<unknown>,
>(
  params?: NetAssetValueReportsGetFundNetAssetValueWithChangesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof netAssetValueReportsGetFundNetAssetValueWithChanges>
        >,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<
            ReturnType<
              typeof netAssetValueReportsGetFundNetAssetValueWithChanges
            >
          >,
          TError,
          Awaited<
            ReturnType<
              typeof netAssetValueReportsGetFundNetAssetValueWithChanges
            >
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useNetAssetValueReportsGetFundNetAssetValueWithChanges<
  TData = Awaited<
    ReturnType<typeof netAssetValueReportsGetFundNetAssetValueWithChanges>
  >,
  TError = ErrorType<unknown>,
>(
  params?: NetAssetValueReportsGetFundNetAssetValueWithChangesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof netAssetValueReportsGetFundNetAssetValueWithChanges>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دریافت قیمت صدور و ابطال بعلاوه تغییرات
 */

export function useNetAssetValueReportsGetFundNetAssetValueWithChanges<
  TData = Awaited<
    ReturnType<typeof netAssetValueReportsGetFundNetAssetValueWithChanges>
  >,
  TError = ErrorType<unknown>,
>(
  params?: NetAssetValueReportsGetFundNetAssetValueWithChangesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof netAssetValueReportsGetFundNetAssetValueWithChanges>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions =
    getNetAssetValueReportsGetFundNetAssetValueWithChangesQueryOptions(
      params,
      options,
    );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary دریافت نمودار قیمت صدور و ابطال
 */
export const netAssetValueReportsGetFundNetAssetValuesChart = (
  params?: NetAssetValueReportsGetFundNetAssetValuesChartParams,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfGetFundNetAssetValuesChartResponse>(
    {
      url: `/api/general/v1/NetAssetValueReports/GetFundNetAssetValuesChart`,
      method: "GET",
      params,
      signal,
    },
    options,
  );
};

export const getNetAssetValueReportsGetFundNetAssetValuesChartQueryKey = (
  params?: NetAssetValueReportsGetFundNetAssetValuesChartParams,
) => {
  return [
    `/api/general/v1/NetAssetValueReports/GetFundNetAssetValuesChart`,
    ...(params ? [params] : []),
  ] as const;
};

export const getNetAssetValueReportsGetFundNetAssetValuesChartQueryOptions = <
  TData = Awaited<
    ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>
  >,
  TError = ErrorType<unknown>,
>(
  params?: NetAssetValueReportsGetFundNetAssetValuesChartParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getNetAssetValueReportsGetFundNetAssetValuesChartQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>>
  > = ({ signal }) =>
    netAssetValueReportsGetFundNetAssetValuesChart(
      params,
      requestOptions,
      signal,
    );

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type NetAssetValueReportsGetFundNetAssetValuesChartQueryResult =
  NonNullable<
    Awaited<ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>>
  >;
export type NetAssetValueReportsGetFundNetAssetValuesChartQueryError =
  ErrorType<unknown>;

export function useNetAssetValueReportsGetFundNetAssetValuesChart<
  TData = Awaited<
    ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>
  >,
  TError = ErrorType<unknown>,
>(
  params: undefined | NetAssetValueReportsGetFundNetAssetValuesChartParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>
        >,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>
          >,
          TError,
          Awaited<
            ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useNetAssetValueReportsGetFundNetAssetValuesChart<
  TData = Awaited<
    ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>
  >,
  TError = ErrorType<unknown>,
>(
  params?: NetAssetValueReportsGetFundNetAssetValuesChartParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>
        >,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>
          >,
          TError,
          Awaited<
            ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useNetAssetValueReportsGetFundNetAssetValuesChart<
  TData = Awaited<
    ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>
  >,
  TError = ErrorType<unknown>,
>(
  params?: NetAssetValueReportsGetFundNetAssetValuesChartParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دریافت نمودار قیمت صدور و ابطال
 */

export function useNetAssetValueReportsGetFundNetAssetValuesChart<
  TData = Awaited<
    ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>
  >,
  TError = ErrorType<unknown>,
>(
  params?: NetAssetValueReportsGetFundNetAssetValuesChartParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof netAssetValueReportsGetFundNetAssetValuesChart>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions =
    getNetAssetValueReportsGetFundNetAssetValuesChartQueryOptions(
      params,
      options,
    );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
