/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useMutation, useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfBoolean,
  ApiResultOfCreateIssuanceOrderByIPGResponse,
  ApiResultOfGetNAVsResponse,
  ApiResultOfNullableGuid,
  CreateIssuanceOrderByIPGCommand,
  FundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody,
  FundIssuanceOrdersGetNAVsParams,
  SendIssuanceOrderOTPCommand,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary دریافت NAV و مقدار حداقل برای صدور
 */
export const fundIssuanceOrdersGetNAVs = (
  params?: FundIssuanceOrdersGetNAVsParams,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfGetNAVsResponse>(
    {
      url: `/api/fundcustomer/v1/FundIssuanceOrders/GetNAVs`,
      method: "GET",
      params,
      signal,
    },
    options,
  );
};

export const getFundIssuanceOrdersGetNAVsQueryKey = (
  params?: FundIssuanceOrdersGetNAVsParams,
) => {
  return [
    `/api/fundcustomer/v1/FundIssuanceOrders/GetNAVs`,
    ...(params ? [params] : []),
  ] as const;
};

export const getFundIssuanceOrdersGetNAVsQueryOptions = <
  TData = Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>,
  TError = ErrorType<unknown>,
>(
  params?: FundIssuanceOrdersGetNAVsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getFundIssuanceOrdersGetNAVsQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>
  > = ({ signal }) => fundIssuanceOrdersGetNAVs(params, requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type FundIssuanceOrdersGetNAVsQueryResult = NonNullable<
  Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>
>;
export type FundIssuanceOrdersGetNAVsQueryError = ErrorType<unknown>;

export function useFundIssuanceOrdersGetNAVs<
  TData = Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>,
  TError = ErrorType<unknown>,
>(
  params: undefined | FundIssuanceOrdersGetNAVsParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>,
          TError,
          Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFundIssuanceOrdersGetNAVs<
  TData = Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>,
  TError = ErrorType<unknown>,
>(
  params?: FundIssuanceOrdersGetNAVsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>,
          TError,
          Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFundIssuanceOrdersGetNAVs<
  TData = Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>,
  TError = ErrorType<unknown>,
>(
  params?: FundIssuanceOrdersGetNAVsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دریافت NAV و مقدار حداقل برای صدور
 */

export function useFundIssuanceOrdersGetNAVs<
  TData = Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>,
  TError = ErrorType<unknown>,
>(
  params?: FundIssuanceOrdersGetNAVsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundIssuanceOrdersGetNAVs>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getFundIssuanceOrdersGetNAVsQueryOptions(
    params,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary ارسال کد یکبار مصرف درخواست صدور
 */
export const fundIssuanceOrdersSendIssuanceOrderOTP = (
  sendIssuanceOrderOTPCommand: SendIssuanceOrderOTPCommand,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfBoolean>(
    {
      url: `/api/fundcustomer/v1/FundIssuanceOrders/SendIssuanceOrderOTP`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: sendIssuanceOrderOTPCommand,
      signal,
    },
    options,
  );
};

export const getFundIssuanceOrdersSendIssuanceOrderOTPMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof fundIssuanceOrdersSendIssuanceOrderOTP>>,
    TError,
    { data: SendIssuanceOrderOTPCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof fundIssuanceOrdersSendIssuanceOrderOTP>>,
  TError,
  { data: SendIssuanceOrderOTPCommand },
  TContext
> => {
  const mutationKey = ["fundIssuanceOrdersSendIssuanceOrderOTP"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof fundIssuanceOrdersSendIssuanceOrderOTP>>,
    { data: SendIssuanceOrderOTPCommand }
  > = (props) => {
    const { data } = props ?? {};

    return fundIssuanceOrdersSendIssuanceOrderOTP(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type FundIssuanceOrdersSendIssuanceOrderOTPMutationResult = NonNullable<
  Awaited<ReturnType<typeof fundIssuanceOrdersSendIssuanceOrderOTP>>
>;
export type FundIssuanceOrdersSendIssuanceOrderOTPMutationBody =
  SendIssuanceOrderOTPCommand;
export type FundIssuanceOrdersSendIssuanceOrderOTPMutationError =
  ErrorType<unknown>;

/**
 * @summary ارسال کد یکبار مصرف درخواست صدور
 */
export const useFundIssuanceOrdersSendIssuanceOrderOTP = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof fundIssuanceOrdersSendIssuanceOrderOTP>>,
      TError,
      { data: SendIssuanceOrderOTPCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof fundIssuanceOrdersSendIssuanceOrderOTP>>,
  TError,
  { data: SendIssuanceOrderOTPCommand },
  TContext
> => {
  const mutationOptions =
    getFundIssuanceOrdersSendIssuanceOrderOTPMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary ثبت درخواست صدور از طریق ارسال فیش بانکی
 */
export const fundIssuanceOrdersCreateIssuanceOrderByBankReceipt = (
  fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody: FundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  const formData = new FormData();
  if (
    fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.FundId !== undefined
  ) {
    formData.append(
      `FundId`,
      fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.FundId,
    );
  }
  if (
    fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.RayanFundBankAccountId !==
    undefined
  ) {
    formData.append(
      `RayanFundBankAccountId`,
      fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.RayanFundBankAccountId.toString(),
    );
  }
  if (
    fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.OrderAmount !==
    undefined
  ) {
    formData.append(
      `OrderAmount`,
      fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.OrderAmount.toString(),
    );
  }
  if (
    fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.ReceiptNumber !==
      undefined &&
    fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.ReceiptNumber !==
      null
  ) {
    formData.append(
      `ReceiptNumber`,
      fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.ReceiptNumber,
    );
  }
  if (
    fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.ReceiptDate !==
    undefined
  ) {
    formData.append(
      `ReceiptDate`,
      fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.ReceiptDate,
    );
  }
  if (
    fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.ReceiptComments !==
      undefined &&
    fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.ReceiptComments !==
      null
  ) {
    formData.append(
      `ReceiptComments`,
      fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.ReceiptComments,
    );
  }
  if (
    fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.OTP !== undefined &&
    fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.OTP !== null
  ) {
    formData.append(
      `OTP`,
      fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.OTP,
    );
  }
  if (
    fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.ReceiptImageFile !==
      undefined &&
    fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.ReceiptImageFile !==
      null
  ) {
    formData.append(
      `ReceiptImageFile`,
      fundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody.ReceiptImageFile,
    );
  }

  return api<ApiResultOfNullableGuid>(
    {
      url: `/api/fundcustomer/v1/FundIssuanceOrders/CreateIssuanceOrderByBankReceipt`,
      method: "POST",
      headers: { "Content-Type": "multipart/form-data" },
      data: formData,
      signal,
    },
    options,
  );
};

export const getFundIssuanceOrdersCreateIssuanceOrderByBankReceiptMutationOptions =
  <TError = ErrorType<unknown>, TContext = unknown>(options?: {
    mutation?: UseMutationOptions<
      Awaited<
        ReturnType<typeof fundIssuanceOrdersCreateIssuanceOrderByBankReceipt>
      >,
      TError,
      { data: FundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  }): UseMutationOptions<
    Awaited<
      ReturnType<typeof fundIssuanceOrdersCreateIssuanceOrderByBankReceipt>
    >,
    TError,
    { data: FundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody },
    TContext
  > => {
    const mutationKey = ["fundIssuanceOrdersCreateIssuanceOrderByBankReceipt"];
    const { mutation: mutationOptions, request: requestOptions } = options
      ? options.mutation &&
        "mutationKey" in options.mutation &&
        options.mutation.mutationKey
        ? options
        : { ...options, mutation: { ...options.mutation, mutationKey } }
      : { mutation: { mutationKey }, request: undefined };

    const mutationFn: MutationFunction<
      Awaited<
        ReturnType<typeof fundIssuanceOrdersCreateIssuanceOrderByBankReceipt>
      >,
      { data: FundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody }
    > = (props) => {
      const { data } = props ?? {};

      return fundIssuanceOrdersCreateIssuanceOrderByBankReceipt(
        data,
        requestOptions,
      );
    };

    return { mutationFn, ...mutationOptions };
  };

export type FundIssuanceOrdersCreateIssuanceOrderByBankReceiptMutationResult =
  NonNullable<
    Awaited<
      ReturnType<typeof fundIssuanceOrdersCreateIssuanceOrderByBankReceipt>
    >
  >;
export type FundIssuanceOrdersCreateIssuanceOrderByBankReceiptMutationBody =
  FundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody;
export type FundIssuanceOrdersCreateIssuanceOrderByBankReceiptMutationError =
  ErrorType<unknown>;

/**
 * @summary ثبت درخواست صدور از طریق ارسال فیش بانکی
 */
export const useFundIssuanceOrdersCreateIssuanceOrderByBankReceipt = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<
        ReturnType<typeof fundIssuanceOrdersCreateIssuanceOrderByBankReceipt>
      >,
      TError,
      { data: FundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<
    ReturnType<typeof fundIssuanceOrdersCreateIssuanceOrderByBankReceipt>
  >,
  TError,
  { data: FundIssuanceOrdersCreateIssuanceOrderByBankReceiptBody },
  TContext
> => {
  const mutationOptions =
    getFundIssuanceOrdersCreateIssuanceOrderByBankReceiptMutationOptions(
      options,
    );

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary ثبت درخواست صدور از طریق پرداخت الکترونیک
 */
export const fundIssuanceOrdersCreateIssuanceOrderByIPG = (
  createIssuanceOrderByIPGCommand: CreateIssuanceOrderByIPGCommand,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfCreateIssuanceOrderByIPGResponse>(
    {
      url: `/api/fundcustomer/v1/FundIssuanceOrders/CreateIssuanceOrderByIPG`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: createIssuanceOrderByIPGCommand,
      signal,
    },
    options,
  );
};

export const getFundIssuanceOrdersCreateIssuanceOrderByIPGMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof fundIssuanceOrdersCreateIssuanceOrderByIPG>>,
    TError,
    { data: CreateIssuanceOrderByIPGCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof fundIssuanceOrdersCreateIssuanceOrderByIPG>>,
  TError,
  { data: CreateIssuanceOrderByIPGCommand },
  TContext
> => {
  const mutationKey = ["fundIssuanceOrdersCreateIssuanceOrderByIPG"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof fundIssuanceOrdersCreateIssuanceOrderByIPG>>,
    { data: CreateIssuanceOrderByIPGCommand }
  > = (props) => {
    const { data } = props ?? {};

    return fundIssuanceOrdersCreateIssuanceOrderByIPG(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type FundIssuanceOrdersCreateIssuanceOrderByIPGMutationResult =
  NonNullable<
    Awaited<ReturnType<typeof fundIssuanceOrdersCreateIssuanceOrderByIPG>>
  >;
export type FundIssuanceOrdersCreateIssuanceOrderByIPGMutationBody =
  CreateIssuanceOrderByIPGCommand;
export type FundIssuanceOrdersCreateIssuanceOrderByIPGMutationError =
  ErrorType<unknown>;

/**
 * @summary ثبت درخواست صدور از طریق پرداخت الکترونیک
 */
export const useFundIssuanceOrdersCreateIssuanceOrderByIPG = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof fundIssuanceOrdersCreateIssuanceOrderByIPG>>,
      TError,
      { data: CreateIssuanceOrderByIPGCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof fundIssuanceOrdersCreateIssuanceOrderByIPG>>,
  TError,
  { data: CreateIssuanceOrderByIPGCommand },
  TContext
> => {
  const mutationOptions =
    getFundIssuanceOrdersCreateIssuanceOrderByIPGMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
