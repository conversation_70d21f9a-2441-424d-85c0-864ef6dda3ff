/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfListOfFundAssetCompositionViewModel,
  ApiResultOfListOfGetFundIndustriesSharePercentViewModel,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary دریافت لیست درصد حجم صنایع در صندوق
 */
export const portfolioReportsGetFundIndustriesSharePercent = (
  fundId: string,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfListOfGetFundIndustriesSharePercentViewModel>(
    {
      url: `/api/general/v1/PortfolioReports/GetFundIndustriesSharePercent/${fundId}`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getPortfolioReportsGetFundIndustriesSharePercentQueryKey = (
  fundId: string,
) => {
  return [
    `/api/general/v1/PortfolioReports/GetFundIndustriesSharePercent/${fundId}`,
  ] as const;
};

export const getPortfolioReportsGetFundIndustriesSharePercentQueryOptions = <
  TData = Awaited<
    ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>
  >,
  TError = ErrorType<unknown>,
>(
  fundId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getPortfolioReportsGetFundIndustriesSharePercentQueryKey(fundId);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>>
  > = ({ signal }) =>
    portfolioReportsGetFundIndustriesSharePercent(
      fundId,
      requestOptions,
      signal,
    );

  return {
    queryKey,
    queryFn,
    enabled: !!fundId,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type PortfolioReportsGetFundIndustriesSharePercentQueryResult =
  NonNullable<
    Awaited<ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>>
  >;
export type PortfolioReportsGetFundIndustriesSharePercentQueryError =
  ErrorType<unknown>;

export function usePortfolioReportsGetFundIndustriesSharePercent<
  TData = Awaited<
    ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>
  >,
  TError = ErrorType<unknown>,
>(
  fundId: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>
        >,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>
          >,
          TError,
          Awaited<
            ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function usePortfolioReportsGetFundIndustriesSharePercent<
  TData = Awaited<
    ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>
  >,
  TError = ErrorType<unknown>,
>(
  fundId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>
        >,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>
          >,
          TError,
          Awaited<
            ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function usePortfolioReportsGetFundIndustriesSharePercent<
  TData = Awaited<
    ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>
  >,
  TError = ErrorType<unknown>,
>(
  fundId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دریافت لیست درصد حجم صنایع در صندوق
 */

export function usePortfolioReportsGetFundIndustriesSharePercent<
  TData = Awaited<
    ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>
  >,
  TError = ErrorType<unknown>,
>(
  fundId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof portfolioReportsGetFundIndustriesSharePercent>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions =
    getPortfolioReportsGetFundIndustriesSharePercentQueryOptions(
      fundId,
      options,
    );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary ترکیب دارایی صندوق - سمت کاربران
 */
export const portfolioReportsGetFundAssetComposition = (
  fundId: string,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfListOfFundAssetCompositionViewModel>(
    {
      url: `/api/general/v1/PortfolioReports/GetFundAssetComposition/${fundId}`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getPortfolioReportsGetFundAssetCompositionQueryKey = (
  fundId: string,
) => {
  return [
    `/api/general/v1/PortfolioReports/GetFundAssetComposition/${fundId}`,
  ] as const;
};

export const getPortfolioReportsGetFundAssetCompositionQueryOptions = <
  TData = Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>,
  TError = ErrorType<unknown>,
>(
  fundId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getPortfolioReportsGetFundAssetCompositionQueryKey(fundId);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>
  > = ({ signal }) =>
    portfolioReportsGetFundAssetComposition(fundId, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!fundId,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type PortfolioReportsGetFundAssetCompositionQueryResult = NonNullable<
  Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>
>;
export type PortfolioReportsGetFundAssetCompositionQueryError =
  ErrorType<unknown>;

export function usePortfolioReportsGetFundAssetComposition<
  TData = Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>,
  TError = ErrorType<unknown>,
>(
  fundId: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>,
          TError,
          Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function usePortfolioReportsGetFundAssetComposition<
  TData = Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>,
  TError = ErrorType<unknown>,
>(
  fundId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>,
          TError,
          Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function usePortfolioReportsGetFundAssetComposition<
  TData = Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>,
  TError = ErrorType<unknown>,
>(
  fundId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary ترکیب دارایی صندوق - سمت کاربران
 */

export function usePortfolioReportsGetFundAssetComposition<
  TData = Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>,
  TError = ErrorType<unknown>,
>(
  fundId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof portfolioReportsGetFundAssetComposition>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getPortfolioReportsGetFundAssetCompositionQueryOptions(
    fundId,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
