/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

export const iPGCallbacksRayanFunds = (
  fundOrderId: string,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<Blob>(
    {
      url: `/api/ipg/v1/callbacks/RayanFunds/${fundOrderId}`,
      method: "GET",
      responseType: "blob",
      signal,
    },
    options,
  );
};

export const getIPGCallbacksRayanFundsQueryKey = (fundOrderId: string) => {
  return [`/api/ipg/v1/callbacks/RayanFunds/${fundOrderId}`] as const;
};

export const getIPGCallbacksRayanFundsQueryOptions = <
  TData = Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>,
  TError = ErrorType<unknown>,
>(
  fundOrderId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getIPGCallbacksRayanFundsQueryKey(fundOrderId);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>
  > = ({ signal }) =>
    iPGCallbacksRayanFunds(fundOrderId, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!fundOrderId,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type IPGCallbacksRayanFundsQueryResult = NonNullable<
  Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>
>;
export type IPGCallbacksRayanFundsQueryError = ErrorType<unknown>;

export function useIPGCallbacksRayanFunds<
  TData = Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>,
  TError = ErrorType<unknown>,
>(
  fundOrderId: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>,
          TError,
          Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useIPGCallbacksRayanFunds<
  TData = Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>,
  TError = ErrorType<unknown>,
>(
  fundOrderId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>,
          TError,
          Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useIPGCallbacksRayanFunds<
  TData = Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>,
  TError = ErrorType<unknown>,
>(
  fundOrderId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};

export function useIPGCallbacksRayanFunds<
  TData = Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>,
  TError = ErrorType<unknown>,
>(
  fundOrderId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof iPGCallbacksRayanFunds>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getIPGCallbacksRayanFundsQueryOptions(
    fundOrderId,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
