/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useMutation, useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfBoolean,
  ApiResultOfGuid,
  ApiResultOfPagingOutputOfPromotionResponse,
  ApiResultOfPromotionResponse,
  CreatePromotionCommand,
  DeletePromotionCommand,
  PromotionsGetPromotionsPagingParams,
  UpdatePromotionCommand,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary دریافت پروموشن فعال
 */
export const promotionsGetActivePromotion = (
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfPromotionResponse>(
    {
      url: `/api/general/v1/Promotions/GetActivePromotion`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getPromotionsGetActivePromotionQueryKey = () => {
  return [`/api/general/v1/Promotions/GetActivePromotion`] as const;
};

export const getPromotionsGetActivePromotionQueryOptions = <
  TData = Awaited<ReturnType<typeof promotionsGetActivePromotion>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof promotionsGetActivePromotion>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof api>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getPromotionsGetActivePromotionQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof promotionsGetActivePromotion>>
  > = ({ signal }) => promotionsGetActivePromotion(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof promotionsGetActivePromotion>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type PromotionsGetActivePromotionQueryResult = NonNullable<
  Awaited<ReturnType<typeof promotionsGetActivePromotion>>
>;
export type PromotionsGetActivePromotionQueryError = ErrorType<unknown>;

export function usePromotionsGetActivePromotion<
  TData = Awaited<ReturnType<typeof promotionsGetActivePromotion>>,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof promotionsGetActivePromotion>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof promotionsGetActivePromotion>>,
          TError,
          Awaited<ReturnType<typeof promotionsGetActivePromotion>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function usePromotionsGetActivePromotion<
  TData = Awaited<ReturnType<typeof promotionsGetActivePromotion>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof promotionsGetActivePromotion>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof promotionsGetActivePromotion>>,
          TError,
          Awaited<ReturnType<typeof promotionsGetActivePromotion>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function usePromotionsGetActivePromotion<
  TData = Awaited<ReturnType<typeof promotionsGetActivePromotion>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof promotionsGetActivePromotion>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دریافت پروموشن فعال
 */

export function usePromotionsGetActivePromotion<
  TData = Awaited<ReturnType<typeof promotionsGetActivePromotion>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof promotionsGetActivePromotion>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getPromotionsGetActivePromotionQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary اضافه کردن پروموشن
 */
export const promotionsCreatePromotion = (
  createPromotionCommand: CreatePromotionCommand,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfGuid>(
    {
      url: `/api/backoffice/v1/Promotions`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: createPromotionCommand,
      signal,
    },
    options,
  );
};

export const getPromotionsCreatePromotionMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof promotionsCreatePromotion>>,
    TError,
    { data: CreatePromotionCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof promotionsCreatePromotion>>,
  TError,
  { data: CreatePromotionCommand },
  TContext
> => {
  const mutationKey = ["promotionsCreatePromotion"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof promotionsCreatePromotion>>,
    { data: CreatePromotionCommand }
  > = (props) => {
    const { data } = props ?? {};

    return promotionsCreatePromotion(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PromotionsCreatePromotionMutationResult = NonNullable<
  Awaited<ReturnType<typeof promotionsCreatePromotion>>
>;
export type PromotionsCreatePromotionMutationBody = CreatePromotionCommand;
export type PromotionsCreatePromotionMutationError = ErrorType<unknown>;

/**
 * @summary اضافه کردن پروموشن
 */
export const usePromotionsCreatePromotion = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof promotionsCreatePromotion>>,
      TError,
      { data: CreatePromotionCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof promotionsCreatePromotion>>,
  TError,
  { data: CreatePromotionCommand },
  TContext
> => {
  const mutationOptions = getPromotionsCreatePromotionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary ویرایش پروموشن
 */
export const promotionsUpdatePromotion = (
  updatePromotionCommand: UpdatePromotionCommand,
  options?: SecondParameter<typeof api>,
) => {
  return api<ApiResultOfBoolean>(
    {
      url: `/api/backoffice/v1/Promotions/UpdatePromotion`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: updatePromotionCommand,
    },
    options,
  );
};

export const getPromotionsUpdatePromotionMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof promotionsUpdatePromotion>>,
    TError,
    { data: UpdatePromotionCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof promotionsUpdatePromotion>>,
  TError,
  { data: UpdatePromotionCommand },
  TContext
> => {
  const mutationKey = ["promotionsUpdatePromotion"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof promotionsUpdatePromotion>>,
    { data: UpdatePromotionCommand }
  > = (props) => {
    const { data } = props ?? {};

    return promotionsUpdatePromotion(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PromotionsUpdatePromotionMutationResult = NonNullable<
  Awaited<ReturnType<typeof promotionsUpdatePromotion>>
>;
export type PromotionsUpdatePromotionMutationBody = UpdatePromotionCommand;
export type PromotionsUpdatePromotionMutationError = ErrorType<unknown>;

/**
 * @summary ویرایش پروموشن
 */
export const usePromotionsUpdatePromotion = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof promotionsUpdatePromotion>>,
      TError,
      { data: UpdatePromotionCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof promotionsUpdatePromotion>>,
  TError,
  { data: UpdatePromotionCommand },
  TContext
> => {
  const mutationOptions = getPromotionsUpdatePromotionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary حذف پروموشن
 */
export const promotionsDeletePromotion = (
  deletePromotionCommand: DeletePromotionCommand,
  options?: SecondParameter<typeof api>,
) => {
  return api<ApiResultOfBoolean>(
    {
      url: `/api/backoffice/v1/Promotions/DeletePromotion`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: deletePromotionCommand,
    },
    options,
  );
};

export const getPromotionsDeletePromotionMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof promotionsDeletePromotion>>,
    TError,
    { data: DeletePromotionCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof promotionsDeletePromotion>>,
  TError,
  { data: DeletePromotionCommand },
  TContext
> => {
  const mutationKey = ["promotionsDeletePromotion"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof promotionsDeletePromotion>>,
    { data: DeletePromotionCommand }
  > = (props) => {
    const { data } = props ?? {};

    return promotionsDeletePromotion(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PromotionsDeletePromotionMutationResult = NonNullable<
  Awaited<ReturnType<typeof promotionsDeletePromotion>>
>;
export type PromotionsDeletePromotionMutationBody = DeletePromotionCommand;
export type PromotionsDeletePromotionMutationError = ErrorType<unknown>;

/**
 * @summary حذف پروموشن
 */
export const usePromotionsDeletePromotion = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof promotionsDeletePromotion>>,
      TError,
      { data: DeletePromotionCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof promotionsDeletePromotion>>,
  TError,
  { data: DeletePromotionCommand },
  TContext
> => {
  const mutationOptions = getPromotionsDeletePromotionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary دریافت پروموشن با آیدی
 */
export const promotionsGetPromotionById = (
  id: string,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfPromotionResponse>(
    {
      url: `/api/backoffice/v1/Promotions/GetPromotionById/${id}`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getPromotionsGetPromotionByIdQueryKey = (id: string) => {
  return [`/api/backoffice/v1/Promotions/GetPromotionById/${id}`] as const;
};

export const getPromotionsGetPromotionByIdQueryOptions = <
  TData = Awaited<ReturnType<typeof promotionsGetPromotionById>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof promotionsGetPromotionById>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getPromotionsGetPromotionByIdQueryKey(id);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof promotionsGetPromotionById>>
  > = ({ signal }) => promotionsGetPromotionById(id, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof promotionsGetPromotionById>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type PromotionsGetPromotionByIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof promotionsGetPromotionById>>
>;
export type PromotionsGetPromotionByIdQueryError = ErrorType<unknown>;

export function usePromotionsGetPromotionById<
  TData = Awaited<ReturnType<typeof promotionsGetPromotionById>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof promotionsGetPromotionById>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof promotionsGetPromotionById>>,
          TError,
          Awaited<ReturnType<typeof promotionsGetPromotionById>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function usePromotionsGetPromotionById<
  TData = Awaited<ReturnType<typeof promotionsGetPromotionById>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof promotionsGetPromotionById>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof promotionsGetPromotionById>>,
          TError,
          Awaited<ReturnType<typeof promotionsGetPromotionById>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function usePromotionsGetPromotionById<
  TData = Awaited<ReturnType<typeof promotionsGetPromotionById>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof promotionsGetPromotionById>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دریافت پروموشن با آیدی
 */

export function usePromotionsGetPromotionById<
  TData = Awaited<ReturnType<typeof promotionsGetPromotionById>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof promotionsGetPromotionById>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getPromotionsGetPromotionByIdQueryOptions(id, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary پیجینگ پروموشن
 */
export const promotionsGetPromotionsPaging = (
  params?: PromotionsGetPromotionsPagingParams,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfPagingOutputOfPromotionResponse>(
    {
      url: `/api/backoffice/v1/Promotions/GetPromotionsPaging`,
      method: "GET",
      params,
      signal,
    },
    options,
  );
};

export const getPromotionsGetPromotionsPagingQueryKey = (
  params?: PromotionsGetPromotionsPagingParams,
) => {
  return [
    `/api/backoffice/v1/Promotions/GetPromotionsPaging`,
    ...(params ? [params] : []),
  ] as const;
};

export const getPromotionsGetPromotionsPagingQueryOptions = <
  TData = Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>,
  TError = ErrorType<unknown>,
>(
  params?: PromotionsGetPromotionsPagingParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getPromotionsGetPromotionsPagingQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>
  > = ({ signal }) =>
    promotionsGetPromotionsPaging(params, requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type PromotionsGetPromotionsPagingQueryResult = NonNullable<
  Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>
>;
export type PromotionsGetPromotionsPagingQueryError = ErrorType<unknown>;

export function usePromotionsGetPromotionsPaging<
  TData = Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>,
  TError = ErrorType<unknown>,
>(
  params: undefined | PromotionsGetPromotionsPagingParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>,
          TError,
          Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function usePromotionsGetPromotionsPaging<
  TData = Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>,
  TError = ErrorType<unknown>,
>(
  params?: PromotionsGetPromotionsPagingParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>,
          TError,
          Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function usePromotionsGetPromotionsPaging<
  TData = Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>,
  TError = ErrorType<unknown>,
>(
  params?: PromotionsGetPromotionsPagingParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary پیجینگ پروموشن
 */

export function usePromotionsGetPromotionsPaging<
  TData = Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>,
  TError = ErrorType<unknown>,
>(
  params?: PromotionsGetPromotionsPagingParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof promotionsGetPromotionsPaging>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getPromotionsGetPromotionsPagingQueryOptions(
    params,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
