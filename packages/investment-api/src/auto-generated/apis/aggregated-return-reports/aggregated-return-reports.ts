/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type {
  AggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBankParams,
  ApiResultOfGetAggregatedReturnOfInstituteComparedToBankResponse,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary دریافت اطلاعات نمودار مقایسه بازدهی صندوق با سپرده بانکی
 */
export const aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank =
  (
    params?: AggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBankParams,
    options?: SecondParameter<typeof api>,
    signal?: AbortSignal,
  ) => {
    return api<ApiResultOfGetAggregatedReturnOfInstituteComparedToBankResponse>(
      {
        url: `/api/general/v1/AggregatedReturnReports/GetAggregatedReturnOfInstituteComparedToBank`,
        method: "GET",
        params,
        signal,
      },
      options,
    );
  };

export const getAggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBankQueryKey =
  (
    params?: AggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBankParams,
  ) => {
    return [
      `/api/general/v1/AggregatedReturnReports/GetAggregatedReturnOfInstituteComparedToBank`,
      ...(params ? [params] : []),
    ] as const;
  };

export const getAggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBankQueryOptions =
  <
    TData = Awaited<
      ReturnType<
        typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
      >
    >,
    TError = ErrorType<unknown>,
  >(
    params?: AggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBankParams,
    options?: {
      query?: Partial<
        UseQueryOptions<
          Awaited<
            ReturnType<
              typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
            >
          >,
          TError,
          TData
        >
      >;
      request?: SecondParameter<typeof api>;
    },
  ) => {
    const { query: queryOptions, request: requestOptions } = options ?? {};

    const queryKey =
      queryOptions?.queryKey ??
      getAggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBankQueryKey(
        params,
      );

    const queryFn: QueryFunction<
      Awaited<
        ReturnType<
          typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
        >
      >
    > = ({ signal }) =>
      aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank(
        params,
        requestOptions,
        signal,
      );

    return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
      Awaited<
        ReturnType<
          typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
        >
      >,
      TError,
      TData
    > & { queryKey: DataTag<QueryKey, TData, TError> };
  };

export type AggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBankQueryResult =
  NonNullable<
    Awaited<
      ReturnType<
        typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
      >
    >
  >;
export type AggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBankQueryError =
  ErrorType<unknown>;

export function useAggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank<
  TData = Awaited<
    ReturnType<
      typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
    >
  >,
  TError = ErrorType<unknown>,
>(
  params:
    | undefined
    | AggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBankParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<
            typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
          >
        >,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<
            ReturnType<
              typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
            >
          >,
          TError,
          Awaited<
            ReturnType<
              typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
            >
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useAggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank<
  TData = Awaited<
    ReturnType<
      typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
    >
  >,
  TError = ErrorType<unknown>,
>(
  params?: AggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBankParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<
            typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
          >
        >,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<
            ReturnType<
              typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
            >
          >,
          TError,
          Awaited<
            ReturnType<
              typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
            >
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useAggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank<
  TData = Awaited<
    ReturnType<
      typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
    >
  >,
  TError = ErrorType<unknown>,
>(
  params?: AggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBankParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<
            typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
          >
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دریافت اطلاعات نمودار مقایسه بازدهی صندوق با سپرده بانکی
 */

export function useAggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank<
  TData = Awaited<
    ReturnType<
      typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
    >
  >,
  TError = ErrorType<unknown>,
>(
  params?: AggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBankParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<
            typeof aggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank
          >
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions =
    getAggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBankQueryOptions(
      params,
      options,
    );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
