/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type { ApiResultOfListOfFinancialInstituteInfoResponse } from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary لیست نهادهای مالی
 */
export const financialInstitutesGetAll = (
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfListOfFinancialInstituteInfoResponse>(
    {
      url: `/api/general/v1/FinancialInstitutes/GetAll`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getFinancialInstitutesGetAllQueryKey = () => {
  return [`/api/general/v1/FinancialInstitutes/GetAll`] as const;
};

export const getFinancialInstitutesGetAllQueryOptions = <
  TData = Awaited<ReturnType<typeof financialInstitutesGetAll>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof financialInstitutesGetAll>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof api>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getFinancialInstitutesGetAllQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof financialInstitutesGetAll>>
  > = ({ signal }) => financialInstitutesGetAll(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof financialInstitutesGetAll>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type FinancialInstitutesGetAllQueryResult = NonNullable<
  Awaited<ReturnType<typeof financialInstitutesGetAll>>
>;
export type FinancialInstitutesGetAllQueryError = ErrorType<unknown>;

export function useFinancialInstitutesGetAll<
  TData = Awaited<ReturnType<typeof financialInstitutesGetAll>>,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof financialInstitutesGetAll>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof financialInstitutesGetAll>>,
          TError,
          Awaited<ReturnType<typeof financialInstitutesGetAll>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFinancialInstitutesGetAll<
  TData = Awaited<ReturnType<typeof financialInstitutesGetAll>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof financialInstitutesGetAll>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof financialInstitutesGetAll>>,
          TError,
          Awaited<ReturnType<typeof financialInstitutesGetAll>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFinancialInstitutesGetAll<
  TData = Awaited<ReturnType<typeof financialInstitutesGetAll>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof financialInstitutesGetAll>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary لیست نهادهای مالی
 */

export function useFinancialInstitutesGetAll<
  TData = Awaited<ReturnType<typeof financialInstitutesGetAll>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof financialInstitutesGetAll>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getFinancialInstitutesGetAllQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary لیست نهادهای مالی
 */
export const financialInstitutesGetAll2 = (
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfListOfFinancialInstituteInfoResponse>(
    {
      url: `/api/backoffice/v1/FinancialInstitutes/GetAll`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getFinancialInstitutesGetAll2QueryKey = () => {
  return [`/api/backoffice/v1/FinancialInstitutes/GetAll`] as const;
};

export const getFinancialInstitutesGetAll2QueryOptions = <
  TData = Awaited<ReturnType<typeof financialInstitutesGetAll2>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof financialInstitutesGetAll2>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof api>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getFinancialInstitutesGetAll2QueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof financialInstitutesGetAll2>>
  > = ({ signal }) => financialInstitutesGetAll2(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof financialInstitutesGetAll2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type FinancialInstitutesGetAll2QueryResult = NonNullable<
  Awaited<ReturnType<typeof financialInstitutesGetAll2>>
>;
export type FinancialInstitutesGetAll2QueryError = ErrorType<unknown>;

export function useFinancialInstitutesGetAll2<
  TData = Awaited<ReturnType<typeof financialInstitutesGetAll2>>,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof financialInstitutesGetAll2>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof financialInstitutesGetAll2>>,
          TError,
          Awaited<ReturnType<typeof financialInstitutesGetAll2>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFinancialInstitutesGetAll2<
  TData = Awaited<ReturnType<typeof financialInstitutesGetAll2>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof financialInstitutesGetAll2>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof financialInstitutesGetAll2>>,
          TError,
          Awaited<ReturnType<typeof financialInstitutesGetAll2>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFinancialInstitutesGetAll2<
  TData = Awaited<ReturnType<typeof financialInstitutesGetAll2>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof financialInstitutesGetAll2>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary لیست نهادهای مالی
 */

export function useFinancialInstitutesGetAll2<
  TData = Awaited<ReturnType<typeof financialInstitutesGetAll2>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof financialInstitutesGetAll2>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getFinancialInstitutesGetAll2QueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
