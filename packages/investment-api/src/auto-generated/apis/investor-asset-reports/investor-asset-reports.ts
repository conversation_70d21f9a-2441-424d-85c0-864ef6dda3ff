/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfGetInvestorConsolidatedFundAssetReportResponse,
  ApiResultOfGetInvestorConsolidatedRecentFundOrdersReportResponse,
  ApiResultOfGetInvestorFundRemainsResponse,
  ApiResultOfListOfGetInvestorAssetHistoryResponse,
  ApiResultOfListOfGetInvestorFundLicensesResponse,
  ApiResultOfPagingOutputOfGetInvestorOrdersResponse,
  ApiResultOfPagingOutputOfGetInvestorStatementsResponse,
  InvestorAssetReportsGetActorInvestorAssetHistoryParams,
  InvestorAssetReportsGetActorInvestorFundLicensesParams,
  InvestorAssetReportsGetActorInvestorFundRemainsParams,
  InvestorAssetReportsGetActorInvestorOrdersParams,
  InvestorAssetReportsGetActorInvestorStatementsParams,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary سفارشات سرمایه گذار جاری
 */
export const investorAssetReportsGetActorInvestorOrders = (
  params?: InvestorAssetReportsGetActorInvestorOrdersParams,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfPagingOutputOfGetInvestorOrdersResponse>(
    {
      url: `/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorOrders`,
      method: "GET",
      params,
      signal,
    },
    options,
  );
};

export const getInvestorAssetReportsGetActorInvestorOrdersQueryKey = (
  params?: InvestorAssetReportsGetActorInvestorOrdersParams,
) => {
  return [
    `/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorOrders`,
    ...(params ? [params] : []),
  ] as const;
};

export const getInvestorAssetReportsGetActorInvestorOrdersQueryOptions = <
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorOrders>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorOrdersParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorOrders>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getInvestorAssetReportsGetActorInvestorOrdersQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorOrders>>
  > = ({ signal }) =>
    investorAssetReportsGetActorInvestorOrders(params, requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorOrders>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type InvestorAssetReportsGetActorInvestorOrdersQueryResult = NonNullable<
  Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorOrders>>
>;
export type InvestorAssetReportsGetActorInvestorOrdersQueryError =
  ErrorType<unknown>;

export function useInvestorAssetReportsGetActorInvestorOrders<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorOrders>
  >,
  TError = ErrorType<unknown>,
>(
  params: undefined | InvestorAssetReportsGetActorInvestorOrdersParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorOrders>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorOrders>
          >,
          TError,
          Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorOrders>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useInvestorAssetReportsGetActorInvestorOrders<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorOrders>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorOrdersParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorOrders>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorOrders>
          >,
          TError,
          Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorOrders>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useInvestorAssetReportsGetActorInvestorOrders<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorOrders>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorOrdersParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorOrders>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary سفارشات سرمایه گذار جاری
 */

export function useInvestorAssetReportsGetActorInvestorOrders<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorOrders>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorOrdersParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorOrders>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions =
    getInvestorAssetReportsGetActorInvestorOrdersQueryOptions(params, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary گردش حساب سرمایه گذار جاری
 */
export const investorAssetReportsGetActorInvestorStatements = (
  params?: InvestorAssetReportsGetActorInvestorStatementsParams,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfPagingOutputOfGetInvestorStatementsResponse>(
    {
      url: `/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorStatements`,
      method: "GET",
      params,
      signal,
    },
    options,
  );
};

export const getInvestorAssetReportsGetActorInvestorStatementsQueryKey = (
  params?: InvestorAssetReportsGetActorInvestorStatementsParams,
) => {
  return [
    `/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorStatements`,
    ...(params ? [params] : []),
  ] as const;
};

export const getInvestorAssetReportsGetActorInvestorStatementsQueryOptions = <
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorStatements>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorStatementsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorStatements>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getInvestorAssetReportsGetActorInvestorStatementsQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorStatements>>
  > = ({ signal }) =>
    investorAssetReportsGetActorInvestorStatements(
      params,
      requestOptions,
      signal,
    );

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorStatements>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type InvestorAssetReportsGetActorInvestorStatementsQueryResult =
  NonNullable<
    Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorStatements>>
  >;
export type InvestorAssetReportsGetActorInvestorStatementsQueryError =
  ErrorType<unknown>;

export function useInvestorAssetReportsGetActorInvestorStatements<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorStatements>
  >,
  TError = ErrorType<unknown>,
>(
  params: undefined | InvestorAssetReportsGetActorInvestorStatementsParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorStatements>
        >,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorStatements>
          >,
          TError,
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorStatements>
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useInvestorAssetReportsGetActorInvestorStatements<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorStatements>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorStatementsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorStatements>
        >,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorStatements>
          >,
          TError,
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorStatements>
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useInvestorAssetReportsGetActorInvestorStatements<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorStatements>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorStatementsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorStatements>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary گردش حساب سرمایه گذار جاری
 */

export function useInvestorAssetReportsGetActorInvestorStatements<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorStatements>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorStatementsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorStatements>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions =
    getInvestorAssetReportsGetActorInvestorStatementsQueryOptions(
      params,
      options,
    );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary گواهی های معتبر سرمایه گذار جاری
 */
export const investorAssetReportsGetActorInvestorFundLicenses = (
  params?: InvestorAssetReportsGetActorInvestorFundLicensesParams,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfListOfGetInvestorFundLicensesResponse>(
    {
      url: `/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorFundLicenses`,
      method: "GET",
      params,
      signal,
    },
    options,
  );
};

export const getInvestorAssetReportsGetActorInvestorFundLicensesQueryKey = (
  params?: InvestorAssetReportsGetActorInvestorFundLicensesParams,
) => {
  return [
    `/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorFundLicenses`,
    ...(params ? [params] : []),
  ] as const;
};

export const getInvestorAssetReportsGetActorInvestorFundLicensesQueryOptions = <
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorFundLicensesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getInvestorAssetReportsGetActorInvestorFundLicensesQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>>
  > = ({ signal }) =>
    investorAssetReportsGetActorInvestorFundLicenses(
      params,
      requestOptions,
      signal,
    );

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<
      ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>
    >,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type InvestorAssetReportsGetActorInvestorFundLicensesQueryResult =
  NonNullable<
    Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>>
  >;
export type InvestorAssetReportsGetActorInvestorFundLicensesQueryError =
  ErrorType<unknown>;

export function useInvestorAssetReportsGetActorInvestorFundLicenses<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>
  >,
  TError = ErrorType<unknown>,
>(
  params: undefined | InvestorAssetReportsGetActorInvestorFundLicensesParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>
        >,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>
          >,
          TError,
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useInvestorAssetReportsGetActorInvestorFundLicenses<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorFundLicensesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>
        >,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>
          >,
          TError,
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useInvestorAssetReportsGetActorInvestorFundLicenses<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorFundLicensesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary گواهی های معتبر سرمایه گذار جاری
 */

export function useInvestorAssetReportsGetActorInvestorFundLicenses<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorFundLicensesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorFundLicenses>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions =
    getInvestorAssetReportsGetActorInvestorFundLicensesQueryOptions(
      params,
      options,
    );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary اطلاعات مانده سرمایه گذار جاری
 */
export const investorAssetReportsGetActorInvestorFundRemains = (
  params?: InvestorAssetReportsGetActorInvestorFundRemainsParams,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfGetInvestorFundRemainsResponse>(
    {
      url: `/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorFundRemains`,
      method: "GET",
      params,
      signal,
    },
    options,
  );
};

export const getInvestorAssetReportsGetActorInvestorFundRemainsQueryKey = (
  params?: InvestorAssetReportsGetActorInvestorFundRemainsParams,
) => {
  return [
    `/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorFundRemains`,
    ...(params ? [params] : []),
  ] as const;
};

export const getInvestorAssetReportsGetActorInvestorFundRemainsQueryOptions = <
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorFundRemainsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getInvestorAssetReportsGetActorInvestorFundRemainsQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>>
  > = ({ signal }) =>
    investorAssetReportsGetActorInvestorFundRemains(
      params,
      requestOptions,
      signal,
    );

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type InvestorAssetReportsGetActorInvestorFundRemainsQueryResult =
  NonNullable<
    Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>>
  >;
export type InvestorAssetReportsGetActorInvestorFundRemainsQueryError =
  ErrorType<unknown>;

export function useInvestorAssetReportsGetActorInvestorFundRemains<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>
  >,
  TError = ErrorType<unknown>,
>(
  params: undefined | InvestorAssetReportsGetActorInvestorFundRemainsParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>
        >,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>
          >,
          TError,
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useInvestorAssetReportsGetActorInvestorFundRemains<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorFundRemainsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>
        >,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>
          >,
          TError,
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useInvestorAssetReportsGetActorInvestorFundRemains<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorFundRemainsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary اطلاعات مانده سرمایه گذار جاری
 */

export function useInvestorAssetReportsGetActorInvestorFundRemains<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorFundRemainsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorFundRemains>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions =
    getInvestorAssetReportsGetActorInvestorFundRemainsQueryOptions(
      params,
      options,
    );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary تغییرات ارزش دارایی سرمایه گذار جاری
 */
export const investorAssetReportsGetActorInvestorAssetHistory = (
  params?: InvestorAssetReportsGetActorInvestorAssetHistoryParams,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfListOfGetInvestorAssetHistoryResponse>(
    {
      url: `/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorAssetHistory`,
      method: "GET",
      params,
      signal,
    },
    options,
  );
};

export const getInvestorAssetReportsGetActorInvestorAssetHistoryQueryKey = (
  params?: InvestorAssetReportsGetActorInvestorAssetHistoryParams,
) => {
  return [
    `/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorAssetHistory`,
    ...(params ? [params] : []),
  ] as const;
};

export const getInvestorAssetReportsGetActorInvestorAssetHistoryQueryOptions = <
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorAssetHistoryParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getInvestorAssetReportsGetActorInvestorAssetHistoryQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>>
  > = ({ signal }) =>
    investorAssetReportsGetActorInvestorAssetHistory(
      params,
      requestOptions,
      signal,
    );

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<
      ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>
    >,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type InvestorAssetReportsGetActorInvestorAssetHistoryQueryResult =
  NonNullable<
    Awaited<ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>>
  >;
export type InvestorAssetReportsGetActorInvestorAssetHistoryQueryError =
  ErrorType<unknown>;

export function useInvestorAssetReportsGetActorInvestorAssetHistory<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>
  >,
  TError = ErrorType<unknown>,
>(
  params: undefined | InvestorAssetReportsGetActorInvestorAssetHistoryParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>
        >,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>
          >,
          TError,
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useInvestorAssetReportsGetActorInvestorAssetHistory<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorAssetHistoryParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>
        >,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>
          >,
          TError,
          Awaited<
            ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useInvestorAssetReportsGetActorInvestorAssetHistory<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorAssetHistoryParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary تغییرات ارزش دارایی سرمایه گذار جاری
 */

export function useInvestorAssetReportsGetActorInvestorAssetHistory<
  TData = Awaited<
    ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>
  >,
  TError = ErrorType<unknown>,
>(
  params?: InvestorAssetReportsGetActorInvestorAssetHistoryParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof investorAssetReportsGetActorInvestorAssetHistory>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions =
    getInvestorAssetReportsGetActorInvestorAssetHistoryQueryOptions(
      params,
      options,
    );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary گزارش تجمیعی دارایی صندوق سرمایه گذار جاری
 */
export const investorAssetReportsGetActorInvestorConsolidatedFundAssetReport = (
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfGetInvestorConsolidatedFundAssetReportResponse>(
    {
      url: `/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorConsolidatedFundAssetReport`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getInvestorAssetReportsGetActorInvestorConsolidatedFundAssetReportQueryKey =
  () => {
    return [
      `/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorConsolidatedFundAssetReport`,
    ] as const;
  };

export const getInvestorAssetReportsGetActorInvestorConsolidatedFundAssetReportQueryOptions =
  <
    TData = Awaited<
      ReturnType<
        typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
      >
    >,
    TError = ErrorType<unknown>,
  >(options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<
            typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
          >
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  }) => {
    const { query: queryOptions, request: requestOptions } = options ?? {};

    const queryKey =
      queryOptions?.queryKey ??
      getInvestorAssetReportsGetActorInvestorConsolidatedFundAssetReportQueryKey();

    const queryFn: QueryFunction<
      Awaited<
        ReturnType<
          typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
        >
      >
    > = ({ signal }) =>
      investorAssetReportsGetActorInvestorConsolidatedFundAssetReport(
        requestOptions,
        signal,
      );

    return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
      Awaited<
        ReturnType<
          typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
        >
      >,
      TError,
      TData
    > & { queryKey: DataTag<QueryKey, TData, TError> };
  };

export type InvestorAssetReportsGetActorInvestorConsolidatedFundAssetReportQueryResult =
  NonNullable<
    Awaited<
      ReturnType<
        typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
      >
    >
  >;
export type InvestorAssetReportsGetActorInvestorConsolidatedFundAssetReportQueryError =
  ErrorType<unknown>;

export function useInvestorAssetReportsGetActorInvestorConsolidatedFundAssetReport<
  TData = Awaited<
    ReturnType<
      typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
    >
  >,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<
            typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
          >
        >,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<
            ReturnType<
              typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
            >
          >,
          TError,
          Awaited<
            ReturnType<
              typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
            >
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useInvestorAssetReportsGetActorInvestorConsolidatedFundAssetReport<
  TData = Awaited<
    ReturnType<
      typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
    >
  >,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<
            typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
          >
        >,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<
            ReturnType<
              typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
            >
          >,
          TError,
          Awaited<
            ReturnType<
              typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
            >
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useInvestorAssetReportsGetActorInvestorConsolidatedFundAssetReport<
  TData = Awaited<
    ReturnType<
      typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
    >
  >,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<
            typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
          >
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary گزارش تجمیعی دارایی صندوق سرمایه گذار جاری
 */

export function useInvestorAssetReportsGetActorInvestorConsolidatedFundAssetReport<
  TData = Awaited<
    ReturnType<
      typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
    >
  >,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<
            typeof investorAssetReportsGetActorInvestorConsolidatedFundAssetReport
          >
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions =
    getInvestorAssetReportsGetActorInvestorConsolidatedFundAssetReportQueryOptions(
      options,
    );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary گزارش تجمیعی از وضعیت و تعداد سفارشات صدور و ابطال اخیر سرمایه گذار جاری
 */
export const investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport =
  (options?: SecondParameter<typeof api>, signal?: AbortSignal) => {
    return api<ApiResultOfGetInvestorConsolidatedRecentFundOrdersReportResponse>(
      {
        url: `/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorConsolidatedRecentFundOrdersReport`,
        method: "GET",
        signal,
      },
      options,
    );
  };

export const getInvestorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReportQueryKey =
  () => {
    return [
      `/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorConsolidatedRecentFundOrdersReport`,
    ] as const;
  };

export const getInvestorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReportQueryOptions =
  <
    TData = Awaited<
      ReturnType<
        typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
      >
    >,
    TError = ErrorType<unknown>,
  >(options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<
            typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
          >
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  }) => {
    const { query: queryOptions, request: requestOptions } = options ?? {};

    const queryKey =
      queryOptions?.queryKey ??
      getInvestorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReportQueryKey();

    const queryFn: QueryFunction<
      Awaited<
        ReturnType<
          typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
        >
      >
    > = ({ signal }) =>
      investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport(
        requestOptions,
        signal,
      );

    return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
      Awaited<
        ReturnType<
          typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
        >
      >,
      TError,
      TData
    > & { queryKey: DataTag<QueryKey, TData, TError> };
  };

export type InvestorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReportQueryResult =
  NonNullable<
    Awaited<
      ReturnType<
        typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
      >
    >
  >;
export type InvestorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReportQueryError =
  ErrorType<unknown>;

export function useInvestorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport<
  TData = Awaited<
    ReturnType<
      typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
    >
  >,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<
            typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
          >
        >,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<
            ReturnType<
              typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
            >
          >,
          TError,
          Awaited<
            ReturnType<
              typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
            >
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useInvestorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport<
  TData = Awaited<
    ReturnType<
      typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
    >
  >,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<
            typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
          >
        >,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<
            ReturnType<
              typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
            >
          >,
          TError,
          Awaited<
            ReturnType<
              typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
            >
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useInvestorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport<
  TData = Awaited<
    ReturnType<
      typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
    >
  >,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<
            typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
          >
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary گزارش تجمیعی از وضعیت و تعداد سفارشات صدور و ابطال اخیر سرمایه گذار جاری
 */

export function useInvestorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport<
  TData = Awaited<
    ReturnType<
      typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
    >
  >,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<
            typeof investorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReport
          >
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions =
    getInvestorAssetReportsGetActorInvestorConsolidatedRecentFundOrdersReportQueryOptions(
      options,
    );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
