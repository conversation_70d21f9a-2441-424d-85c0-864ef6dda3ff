/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfGetFundOrderInfoByIdResponse,
  FundOrdersReportsGetActorFundOrderInfoByIdParams,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary دریافت اطلاعات سفارش سرمایه گذار جاری
 */
export const fundOrdersReportsGetActorFundOrderInfoById = (
  params?: FundOrdersReportsGetActorFundOrderInfoByIdParams,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfGetFundOrderInfoByIdResponse>(
    {
      url: `/api/fundcustomer/v1/FundOrdersReports/GetActorFundOrderInfoById`,
      method: "GET",
      params,
      signal,
    },
    options,
  );
};

export const getFundOrdersReportsGetActorFundOrderInfoByIdQueryKey = (
  params?: FundOrdersReportsGetActorFundOrderInfoByIdParams,
) => {
  return [
    `/api/fundcustomer/v1/FundOrdersReports/GetActorFundOrderInfoById`,
    ...(params ? [params] : []),
  ] as const;
};

export const getFundOrdersReportsGetActorFundOrderInfoByIdQueryOptions = <
  TData = Awaited<
    ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>
  >,
  TError = ErrorType<unknown>,
>(
  params?: FundOrdersReportsGetActorFundOrderInfoByIdParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getFundOrdersReportsGetActorFundOrderInfoByIdQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>>
  > = ({ signal }) =>
    fundOrdersReportsGetActorFundOrderInfoById(params, requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type FundOrdersReportsGetActorFundOrderInfoByIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>>
>;
export type FundOrdersReportsGetActorFundOrderInfoByIdQueryError =
  ErrorType<unknown>;

export function useFundOrdersReportsGetActorFundOrderInfoById<
  TData = Awaited<
    ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>
  >,
  TError = ErrorType<unknown>,
>(
  params: undefined | FundOrdersReportsGetActorFundOrderInfoByIdParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>
          >,
          TError,
          Awaited<ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFundOrdersReportsGetActorFundOrderInfoById<
  TData = Awaited<
    ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>
  >,
  TError = ErrorType<unknown>,
>(
  params?: FundOrdersReportsGetActorFundOrderInfoByIdParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>
          >,
          TError,
          Awaited<ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFundOrdersReportsGetActorFundOrderInfoById<
  TData = Awaited<
    ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>
  >,
  TError = ErrorType<unknown>,
>(
  params?: FundOrdersReportsGetActorFundOrderInfoByIdParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دریافت اطلاعات سفارش سرمایه گذار جاری
 */

export function useFundOrdersReportsGetActorFundOrderInfoById<
  TData = Awaited<
    ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>
  >,
  TError = ErrorType<unknown>,
>(
  params?: FundOrdersReportsGetActorFundOrderInfoByIdParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundOrdersReportsGetActorFundOrderInfoById>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions =
    getFundOrdersReportsGetActorFundOrderInfoByIdQueryOptions(params, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
