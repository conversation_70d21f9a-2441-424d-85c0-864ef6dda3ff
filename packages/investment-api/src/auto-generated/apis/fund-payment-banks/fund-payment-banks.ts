/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfListOfFundBankAccountResponse,
  ApiResultOfListOfFundIPGBankResponse,
  FundPaymentBanksGetBankAccountsParams,
  FundPaymentBanksGetIPGBanksParams,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary درگاه های پرداخت الکترونیک صندوق
 */
export const fundPaymentBanksGetIPGBanks = (
  params?: FundPaymentBanksGetIPGBanksParams,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfListOfFundIPGBankResponse>(
    {
      url: `/api/fundcustomer/v1/FundPaymentBanks/GetIPGBanks`,
      method: "GET",
      params,
      signal,
    },
    options,
  );
};

export const getFundPaymentBanksGetIPGBanksQueryKey = (
  params?: FundPaymentBanksGetIPGBanksParams,
) => {
  return [
    `/api/fundcustomer/v1/FundPaymentBanks/GetIPGBanks`,
    ...(params ? [params] : []),
  ] as const;
};

export const getFundPaymentBanksGetIPGBanksQueryOptions = <
  TData = Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>,
  TError = ErrorType<unknown>,
>(
  params?: FundPaymentBanksGetIPGBanksParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getFundPaymentBanksGetIPGBanksQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>
  > = ({ signal }) =>
    fundPaymentBanksGetIPGBanks(params, requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type FundPaymentBanksGetIPGBanksQueryResult = NonNullable<
  Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>
>;
export type FundPaymentBanksGetIPGBanksQueryError = ErrorType<unknown>;

export function useFundPaymentBanksGetIPGBanks<
  TData = Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>,
  TError = ErrorType<unknown>,
>(
  params: undefined | FundPaymentBanksGetIPGBanksParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>,
          TError,
          Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFundPaymentBanksGetIPGBanks<
  TData = Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>,
  TError = ErrorType<unknown>,
>(
  params?: FundPaymentBanksGetIPGBanksParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>,
          TError,
          Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFundPaymentBanksGetIPGBanks<
  TData = Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>,
  TError = ErrorType<unknown>,
>(
  params?: FundPaymentBanksGetIPGBanksParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary درگاه های پرداخت الکترونیک صندوق
 */

export function useFundPaymentBanksGetIPGBanks<
  TData = Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>,
  TError = ErrorType<unknown>,
>(
  params?: FundPaymentBanksGetIPGBanksParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundPaymentBanksGetIPGBanks>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getFundPaymentBanksGetIPGBanksQueryOptions(
    params,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary حساب های بانکی صندوق
 */
export const fundPaymentBanksGetBankAccounts = (
  params?: FundPaymentBanksGetBankAccountsParams,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfListOfFundBankAccountResponse>(
    {
      url: `/api/fundcustomer/v1/FundPaymentBanks/GetBankAccounts`,
      method: "GET",
      params,
      signal,
    },
    options,
  );
};

export const getFundPaymentBanksGetBankAccountsQueryKey = (
  params?: FundPaymentBanksGetBankAccountsParams,
) => {
  return [
    `/api/fundcustomer/v1/FundPaymentBanks/GetBankAccounts`,
    ...(params ? [params] : []),
  ] as const;
};

export const getFundPaymentBanksGetBankAccountsQueryOptions = <
  TData = Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>,
  TError = ErrorType<unknown>,
>(
  params?: FundPaymentBanksGetBankAccountsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getFundPaymentBanksGetBankAccountsQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>
  > = ({ signal }) =>
    fundPaymentBanksGetBankAccounts(params, requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type FundPaymentBanksGetBankAccountsQueryResult = NonNullable<
  Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>
>;
export type FundPaymentBanksGetBankAccountsQueryError = ErrorType<unknown>;

export function useFundPaymentBanksGetBankAccounts<
  TData = Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>,
  TError = ErrorType<unknown>,
>(
  params: undefined | FundPaymentBanksGetBankAccountsParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>,
          TError,
          Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFundPaymentBanksGetBankAccounts<
  TData = Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>,
  TError = ErrorType<unknown>,
>(
  params?: FundPaymentBanksGetBankAccountsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>,
          TError,
          Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFundPaymentBanksGetBankAccounts<
  TData = Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>,
  TError = ErrorType<unknown>,
>(
  params?: FundPaymentBanksGetBankAccountsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary حساب های بانکی صندوق
 */

export function useFundPaymentBanksGetBankAccounts<
  TData = Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>,
  TError = ErrorType<unknown>,
>(
  params?: FundPaymentBanksGetBankAccountsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fundPaymentBanksGetBankAccounts>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getFundPaymentBanksGetBankAccountsQueryOptions(
    params,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
