{"x-generator": "NSwag v14.4.0.0 (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))", "openapi": "3.0.0", "info": {"title": "Investment API", "description": "Saba Tamin Investment Project", "termsOfService": "https://otcsaba.ir", "contact": {"name": "Contact Us", "url": "https://otcsaba.ir"}, "license": {"name": "Terms And Services", "url": "https://otcsaba.ir"}, "version": "v1"}, "servers": [{"url": "http://***************:5100"}], "paths": {"/api/general/v1/Auth/LoginByPassword": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "ورود به سیستم با کلمه عبور", "operationId": "Auth_LoginByPassword", "requestBody": {"content": {"multipart/form-data": {"schema": {"properties": {"nationalCodeOrMobileNumber": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/general/v1/Auth/GetLoginOTPSecretKey/{mobileNumber}": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "دریا<PERSON>ت کلید امنیتی برای ارسال کد یکبار مصرف", "operationId": "Auth_GetLoginOTPSecretKey", "parameters": [{"name": "mobileNumber", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGuid"}}}}}}}, "/api/general/v1/Auth/SendLoginOTP": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "ارسال کد یکبار مصرف", "operationId": "Auth_SendLoginOTP", "requestBody": {"content": {"multipart/form-data": {"schema": {"properties": {"secretKey": {"type": "string", "format": "guid"}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetOtpResponse"}}}}}}}, "/api/general/v1/Auth/LoginByOTP": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "ورود به سیستم با کد یکبار مصرف", "operationId": "Auth_LoginByOTP", "requestBody": {"content": {"multipart/form-data": {"schema": {"properties": {"secretKey": {"type": "string", "format": "guid"}, "otp": {"type": "string", "nullable": true}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/general/v1/Auth/Logout": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "خروج از سیستم", "operationId": "<PERSON><PERSON><PERSON><PERSON>", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/general/v1/Banners/GetAllActiveBanners": {"get": {"tags": ["Banners"], "summary": "دریافت لیست بنرهای فعال", "operationId": "Banners_GetAllActiveBanners", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfListOfBannerResponse"}}}}}}}, "/api/general/v1/Captcha/WhichCaptchaIsActive": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "کدام نوع کپچا فعال است", "operationId": "Captcha_WhichCaptchaIsActive", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfCaptchaTypeEnum"}}}}}}}, "/api/general/v1/Captcha/RequestTextCaptcha": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "درخواست کپچای متنی", "operationId": "Captcha_RequestTextCaptcha", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfRequestCaptchaOutputDTO"}}}}}}}, "/api/customer/v1/ChangePassword/CheckActorUserPassword": {"post": {"tags": ["ChangePassword"], "summary": "چک کردن کلمه عبور", "operationId": "ChangePassword_CheckActorUserPassword", "requestBody": {"x-name": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckActorUserPasswordQuery"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/customer/v1/ChangePassword/ChangeActorUserPassword": {"put": {"tags": ["ChangePassword"], "summary": "تغییر کلمه عبور", "operationId": "ChangePassword_ChangeActorUserPassword", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeActorUserPasswordCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/general/v1/FileManager/ShowImage/{fileName}": {"get": {"tags": ["FileManager"], "summary": "نمایش تصویر - سمت کاربران", "operationId": "FileManager_ShowImage", "parameters": [{"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/general/v1/FileManager/DownloadFile/{fileName}": {"get": {"tags": ["FileManager"], "summary": "د<PERSON><PERSON><PERSON>د فایل - سمت کاربران", "operationId": "FileManager_DownloadFile", "parameters": [{"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/general/v1/FinancialInstitutes/GetAll": {"get": {"tags": ["FinancialInstitutes"], "summary": "لیست نهادهای مالی", "operationId": "FinancialInstitutes_GetAll", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfListOfFinancialInstituteInfoResponse"}}}}}}}, "/api/customer/v1/ForgetPassword/SendOTP": {"post": {"tags": ["ForgetPassword"], "summary": "فراموشی کلمه عبور - ارسال کد یکبار مصرف", "operationId": "ForgetPassword_SendOTP", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgetPasswordSendOTPCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetOtpWithSecretKeyResponse"}}}}}}}, "/api/customer/v1/ForgetPassword/CheckOTP": {"post": {"tags": ["ForgetPassword"], "summary": "فراموشی کلمه عبور - چک کردن کد یکبار مصرف", "operationId": "ForgetPassword_CheckOTP", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgetPasswordCheckOTPCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGuid"}}}}}}}, "/api/customer/v1/ForgetPassword/SetNewPassword": {"post": {"tags": ["ForgetPassword"], "summary": "فراموشی کلمه عبور - تنظیم کلمه عبور جدید", "operationId": "ForgetPassword_SetNewPassword", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgetPasswordSetNewPasswordCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGuid"}}}}}}}, "/api/fundcustomer/v1/FundIssuanceOrders/GetNAVs": {"get": {"tags": ["FundIssuanceOrders"], "summary": "دریافت NAV و مقدار حداقل برای صدور", "operationId": "FundIssuanceOrders_GetNAVs", "parameters": [{"name": "FundId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetNAVsResponse"}}}}}}}, "/api/fundcustomer/v1/FundIssuanceOrders/SendIssuanceOrderOTP": {"post": {"tags": ["FundIssuanceOrders"], "summary": "ارسال کد یکبار مصرف درخواست صدور", "operationId": "FundIssuanceOrders_SendIssuanceOrderOTP", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendIssuanceOrderOTPCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/fundcustomer/v1/FundIssuanceOrders/CreateIssuanceOrderByBankReceipt": {"post": {"tags": ["FundIssuanceOrders"], "summary": "ثبت درخواست صدور از طریق ارسال فیش بانکی", "operationId": "FundIssuanceOrders_CreateIssuanceOrderByBankReceipt", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"FundId": {"type": "string", "format": "guid"}, "RayanFundBankAccountId": {"type": "integer", "format": "int64"}, "OrderAmount": {"type": "integer", "format": "int64"}, "ReceiptNumber": {"type": "string", "nullable": true}, "ReceiptDate": {"type": "string", "format": "date-time"}, "ReceiptComments": {"type": "string", "nullable": true}, "OTP": {"type": "string", "nullable": true}, "ReceiptImageFile": {"type": "string", "format": "binary", "nullable": true}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfNullableGuid"}}}}}}}, "/api/fundcustomer/v1/FundIssuanceOrders/CreateIssuanceOrderByIPG": {"post": {"tags": ["FundIssuanceOrders"], "summary": "ثبت درخواست صدور از طریق پرداخت الکترونیک", "operationId": "FundIssuanceOrders_CreateIssuanceOrderByIPG", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIssuanceOrderByIPGCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfCreateIssuanceOrderByIPGResponse"}}}}}}}, "/api/fundcustomer/v1/FundPaymentBanks/GetIPGBanks": {"get": {"tags": ["FundPaymentBanks"], "summary": "درگاه های پرداخت الکترونیک صندوق", "operationId": "FundPaymentBanks_GetIPGBanks", "parameters": [{"name": "FundId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfListOfFundIPGBankResponse"}}}}}}}, "/api/fundcustomer/v1/FundPaymentBanks/GetBankAccounts": {"get": {"tags": ["FundPaymentBanks"], "summary": "حساب های بانکی صندوق", "operationId": "FundPaymentBanks_GetBankAccounts", "parameters": [{"name": "FundId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfListOfFundBankAccountResponse"}}}}}}}, "/api/fundcustomer/v1/FundRevokeOrders/SendRevokeOrderOTP": {"post": {"tags": ["FundRevokeOrders"], "summary": "ارسال کد یکبار مصرف درخواست ابطال", "operationId": "FundRevokeOrders_SendRevokeOrderOTP", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendRevokeOrderOTPCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/fundcustomer/v1/FundRevokeOrders/CreateRevokeOrder": {"post": {"tags": ["FundRevokeOrders"], "summary": "ثبت درخواست ابطال", "operationId": "FundRevokeOrders_CreateRevokeOrder", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRevokeOrderCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfNullableGuid"}}}}}}}, "/api/ipg/v1/callbacks/RayanFunds/{fundOrderId}": {"get": {"tags": ["IPGCallbacks"], "operationId": "IPGCallbacks_RayanFunds", "parameters": [{"name": "fundOrderId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/general/v1/Profile": {"get": {"tags": ["Profile"], "summary": "پروفایل کاربری", "operationId": "Profile_GetLoggedInUserProfile", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetLoggedInUserProfileResponse"}}}}}}}, "/api/general/v1/Promotions/GetActivePromotion": {"get": {"tags": ["Promotions"], "summary": "دریافت پروموشن فعال", "operationId": "Promotions_GetActivePromotion", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfPromotionResponse"}}}}}}}, "/api/general/v1/Sejam/IsSejami": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "چک کردن سجامی بودن کاربر", "operationId": "Sejam_IsSejami", "requestBody": {"x-name": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IsSejamiQuery"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/general/v1/Sejam/KycOTP": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "ارسال کد یکبار مصرف سجام", "operationId": "Sejam_KycOTP", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KycOTPCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/general/v1/Sejam/ProfileInquiry": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "دریا<PERSON>ت کلید امنیتی پروفایل سجام", "operationId": "Sejam_ProfileInquiry", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfileInquiryCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGuid"}}}}}}}, "/api/general/v1/Sejam/ProfileInquiryShow": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "نمایش پروفایل سجام با کلید امنیتی", "operationId": "Sejam_ProfileInquiryShow", "parameters": [{"name": "FinancialInstituteId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfSejamProfileResponse"}}}}}}}, "/api/general/v1/Sejam/SetAsCustomerProfile": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "ایجاد پروفایل کاربری برای ویزیتور", "operationId": "Sejam_SetAsCustomerProfile", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SetAsCustomerProfileCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/general/v1/Sejam/UpdateProfile": {"put": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "ویرایش پروفایل مشتری", "operationId": "Sejam_UpdateProfile", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProfileCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/general/v1/UserFrontendConfig/EditUserConfig": {"put": {"tags": ["UserFrontendConfig"], "summary": "ویرایش تنظیمات کاربر", "operationId": "UserFrontendConfig_EditUserConfig", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EditCurrentUserFrontendConfigCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/general/v1/UserFrontendConfig/GetUserConfig": {"get": {"tags": ["UserFrontendConfig"], "summary": "دریافت تنظیمات کاربر", "operationId": "UserFrontendConfig_GetUserConfig", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfString"}}}}}}}, "/api/general/v1/AggregatedReturnReports/GetAggregatedReturnOfInstituteComparedToBank": {"get": {"tags": ["AggregatedReturnReports"], "summary": "دریافت اطلاعات نمودار مقایسه بازدهی صندوق با سپرده بانکی", "operationId": "AggregatedReturnReports_GetAggregatedReturnOfInstituteComparedToBank", "parameters": [{"name": "FinancialInstituteId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "FromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}, "x-position": 2}, {"name": "ToDate", "in": "query", "schema": {"type": "string", "format": "date-time"}, "x-position": 3}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetAggregatedReturnOfInstituteComparedToBankResponse"}}}}}}}, "/api/fundcustomer/v1/FundOrdersReports/GetActorFundOrderInfoById": {"get": {"tags": ["FundOrdersReports"], "summary": "دریافت اطلاعات سفارش سرمایه گذار جاری", "operationId": "FundOrdersReports_GetActorFundOrderInfoById", "parameters": [{"name": "FundOrderId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetFundOrderInfoByIdResponse"}}}}}}}, "/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorOrders": {"get": {"tags": ["InvestorAssetReports"], "summary": "سفارشات سرمایه گذار جاری", "operationId": "InvestorAssetReports_GetActorInvestorOrders", "parameters": [{"name": "FundOrderType", "in": "query", "schema": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/FundOrderTypeEnum"}]}, "x-position": 1}, {"name": "FromDate", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 2}, {"name": "ToDate", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 3}, {"name": "FundId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 4}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}, "x-position": 5}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}, "x-position": 6}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfPagingOutputOfGetInvestorOrdersResponse"}}}}}}}, "/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorStatements": {"get": {"tags": ["InvestorAssetReports"], "summary": "گردش حساب سرمایه گذار جاری", "operationId": "InvestorAssetReports_GetActorInvestorStatements", "parameters": [{"name": "FromDate", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 1}, {"name": "ToDate", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 2}, {"name": "FundId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 3}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}, "x-position": 4}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}, "x-position": 5}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfPagingOutputOfGetInvestorStatementsResponse"}}}}}}}, "/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorFundLicenses": {"get": {"tags": ["InvestorAssetReports"], "summary": "گواهی های معتبر سرمایه گذار جاری", "operationId": "InvestorAssetReports_GetActorInvestorFundLicenses", "parameters": [{"name": "FundId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfListOfGetInvestorFundLicensesResponse"}}}}}}}, "/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorFundRemains": {"get": {"tags": ["InvestorAssetReports"], "summary": "اطلاعات مانده سرمایه گذار جاری", "operationId": "InvestorAssetReports_GetActorInvestorFundRemains", "parameters": [{"name": "FundId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetInvestorFundRemainsResponse"}}}}}}}, "/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorAssetHistory": {"get": {"tags": ["InvestorAssetReports"], "summary": "تغییرات ارزش دارایی سرمایه گذار جاری", "operationId": "InvestorAssetReports_GetActorInvestorAssetHistory", "parameters": [{"name": "FundId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "FromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}, "x-position": 2}, {"name": "ToDate", "in": "query", "schema": {"type": "string", "format": "date-time"}, "x-position": 3}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfListOfGetInvestorAssetHistoryResponse"}}}}}}}, "/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorConsolidatedFundAssetReport": {"get": {"tags": ["InvestorAssetReports"], "summary": "گزارش تجمیعی دارایی صندوق سرمایه گذار جاری", "operationId": "InvestorAssetReports_GetActorInvestorConsolidatedFundAssetReport", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetInvestorConsolidatedFundAssetReportResponse"}}}}}}}, "/api/fundcustomer/v1/InvestorAssetReports/GetActorInvestorConsolidatedRecentFundOrdersReport": {"get": {"tags": ["InvestorAssetReports"], "summary": "گزارش تجمیعی از وضعیت و تعداد سفارشات صدور و ابطال اخیر سرمایه گذار جاری", "operationId": "InvestorAssetReports_GetActorInvestorConsolidatedRecentFundOrdersReport", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetInvestorConsolidatedRecentFundOrdersReportResponse"}}}}}}}, "/api/general/v1/NetAssetValueReports/GetFundTotalNetAssetValue": {"get": {"tags": ["NetAssetValueReports"], "summary": "دریافت خالص ارزش دارایی صندوق", "operationId": "NetAssetValueReports_GetFundTotalNetAssetValue", "parameters": [{"name": "FundId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetFundTotalNetAssetValueResponse"}}}}}}}, "/api/general/v1/NetAssetValueReports/GetFundNetAssetValueWithChanges": {"get": {"tags": ["NetAssetValueReports"], "summary": "دریا<PERSON>ت قیمت صدور و ابطال بعلاوه تغییرات", "operationId": "NetAssetValueReports_GetFundNetAssetValueWithChanges", "parameters": [{"name": "FundId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetFundNetAssetValueWithChangesResponse"}}}}}}}, "/api/general/v1/NetAssetValueReports/GetFundNetAssetValuesChart": {"get": {"tags": ["NetAssetValueReports"], "summary": "دریافت نمودار قیمت صدور و ابطال", "operationId": "NetAssetValueReports_GetFundNetAssetValuesChart", "parameters": [{"name": "FundId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "FromDate", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetFundNetAssetValuesChartResponse"}}}}}}}, "/api/general/v1/PortfolioReports/GetFundIndustriesSharePercent/{FundId}": {"get": {"tags": ["PortfolioReports"], "summary": "دریافت لیست درصد حجم صنایع در صندوق", "operationId": "PortfolioReports_GetFundIndustriesSharePercent", "parameters": [{"name": "FundId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfListOfGetFundIndustriesSharePercentViewModel"}}}}}}}, "/api/general/v1/PortfolioReports/GetFundAssetComposition/{FundId}": {"get": {"tags": ["PortfolioReports"], "summary": "ترکیب دارایی صندوق - سمت کاربران", "operationId": "PortfolioReports_GetFundAssetComposition", "parameters": [{"name": "FundId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfListOfFundAssetCompositionViewModel"}}}}}}}, "/api/general/v1/SimpleReturnReports/GetFundTimePeriodSimpleReturns/{FundId}": {"get": {"tags": ["SimpleReturnReports"], "summary": "دریافت بازدهی صندوق در دوره های زمانی 1،3،6 و 12 ماهه", "operationId": "SimpleReturnReports_GetFundTimePeriodSimpleReturns", "parameters": [{"name": "FundId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetFundTimePeriodBasedSimpleReturnsResponse"}}}}}}}, "/api/backoffice/v1/Auth/LoginByPassword": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "ورود به سیستم با کلمه عبور", "operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requestBody": {"content": {"multipart/form-data": {"schema": {"properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/backoffice/v1/Auth/Logout": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "خروج از سیستم", "operationId": "Auth_Logout2", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/backoffice/v1/Banners": {"post": {"tags": ["Banners"], "summary": "اضافه کردن بنر", "operationId": "Banners_CreateBanner", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBannerCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGuid"}}}}}}}, "/api/backoffice/v1/Banners/UpdateBanner": {"put": {"tags": ["Banners"], "summary": "ویرایش بنر", "operationId": "Banners_UpdateBanner", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBannerCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/backoffice/v1/Banners/DeleteBanner": {"put": {"tags": ["Banners"], "summary": "<PERSON><PERSON><PERSON> بنر", "operationId": "Banners_DeleteBanner", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteBannerCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/backoffice/v1/Banners/GetBannerById/{Id}": {"get": {"tags": ["Banners"], "summary": "دریافت بنر با آیدی", "operationId": "Banners_GetBannerById", "parameters": [{"name": "Id", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBannerResponse"}}}}}}}, "/api/backoffice/v1/Banners/GetBannersPaging": {"get": {"tags": ["Banners"], "summary": "پیجینگ بنر", "operationId": "Banners_GetBannersPaging", "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfPagingOutputOfBannerResponse"}}}}}}}, "/api/backoffice/v1/Banners/GetBannersCount": {"get": {"tags": ["Banners"], "summary": "تعداد بنرها", "operationId": "Banners_GetBannersCount", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBannersCountResponse"}}}}}}}, "/api/backoffice/v1/Captcha/WhichCaptchaIsActive": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "کدام نوع کپچا فعال است", "operationId": "Captcha_WhichCaptchaIsActive2", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfCaptchaTypeEnum"}}}}}}}, "/api/backoffice/v1/Captcha/RequestTextCaptcha": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "درخواست کپچای متنی", "operationId": "Captcha_RequestTextCaptcha2", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfRequestCaptchaOutputDTO"}}}}}}}, "/api/backoffice/v1/ChangePassword": {"put": {"tags": ["ChangePassword"], "summary": "تغییر کلمه عبور", "operationId": "ChangePassword_ChangeActorUserPassword2", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeActorUserPasswordCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/backoffice/v1/FileManager/SaveImage": {"post": {"tags": ["FileManager"], "summary": "ذخیره تصویر", "operationId": "FileManager_SaveImage", "requestBody": {"content": {"multipart/form-data": {"schema": {"properties": {"ContentType": {"type": "string", "nullable": true}, "ContentDisposition": {"type": "string", "nullable": true}, "Headers": {"type": "array", "nullable": true, "items": {}}, "Length": {"type": "integer", "format": "int64"}, "Name": {"type": "string", "nullable": true}, "FileName": {"type": "string", "nullable": true}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfString"}}}}}}}, "/api/backoffice/v1/FileManager/ShowImage/{fileName}": {"get": {"tags": ["FileManager"], "summary": "نمایش تصویر - بک آفیس", "operationId": "FileManager_ShowImage2", "parameters": [{"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/backoffice/v1/FileManager/SaveFile": {"post": {"tags": ["FileManager"], "summary": "ذخیره فایل", "operationId": "FileManager_SaveFile", "requestBody": {"content": {"multipart/form-data": {"schema": {"properties": {"ContentType": {"type": "string", "nullable": true}, "ContentDisposition": {"type": "string", "nullable": true}, "Headers": {"type": "array", "nullable": true, "items": {}}, "Length": {"type": "integer", "format": "int64"}, "Name": {"type": "string", "nullable": true}, "FileName": {"type": "string", "nullable": true}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfString"}}}}}}}, "/api/backoffice/v1/FileManager/DownloadFile/{fileName}": {"get": {"tags": ["FileManager"], "summary": "د<PERSON><PERSON><PERSON>د فایل - بک آفیس", "operationId": "FileManager_DownloadFile2", "parameters": [{"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/backoffice/v1/FinancialInstitutes/GetAll": {"get": {"tags": ["FinancialInstitutes"], "summary": "لیست نهادهای مالی", "operationId": "FinancialInstitutes_GetAll2", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfListOfFinancialInstituteInfoResponse"}}}}}}}, "/api/backoffice/v1/Profile": {"get": {"tags": ["Profile"], "summary": "پروفایل کاربری", "operationId": "Profile_GetLoggedInUserProfile2", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetLoggedInUserProfileResponse"}}}}}}}, "/api/backoffice/v1/Promotions": {"post": {"tags": ["Promotions"], "summary": "اضافه کردن پروموشن", "operationId": "Promotions_CreatePromotion", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePromotionCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGuid"}}}}}}}, "/api/backoffice/v1/Promotions/UpdatePromotion": {"put": {"tags": ["Promotions"], "summary": "ویرایش پروموشن", "operationId": "Promotions_UpdatePromotion", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePromotionCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/backoffice/v1/Promotions/DeletePromotion": {"put": {"tags": ["Promotions"], "summary": "حذف پروموشن", "operationId": "Promotions_DeletePromotion", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeletePromotionCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/backoffice/v1/Promotions/GetPromotionById/{Id}": {"get": {"tags": ["Promotions"], "summary": "دریافت پروموشن با آیدی", "operationId": "Promotions_GetPromotionById", "parameters": [{"name": "Id", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfPromotionResponse"}}}}}}}, "/api/backoffice/v1/Promotions/GetPromotionsPaging": {"get": {"tags": ["Promotions"], "summary": "پیجینگ پروموشن", "operationId": "Promotions_GetPromotionsPaging", "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfPagingOutputOfPromotionResponse"}}}}}}}, "/api/backoffice/v1/Settings/GetSystemSettings": {"get": {"tags": ["Settings"], "summary": "دریافت تنظیمات سیستم", "operationId": "Settings_GetSystemSettings", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfSystemSettingsResponse"}}}}}}}, "/api/backoffice/v1/Settings/EditSystemSettings": {"put": {"tags": ["Settings"], "summary": "ویرایش تنظیمات سیستم", "operationId": "Settings_EditSystemSettings", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EditSystemSettingsCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/backoffice/v1/Settings/GetCaptchaSettingsHistoryPaging": {"get": {"tags": ["Settings"], "summary": "دریافت تاریخچه تغییرات تنظیمات کپچا", "operationId": "Settings_GetCaptchaSettingsHistoryPaging", "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfPagingOutputOfCaptchaSettingsHistoryResponse"}}}}}}}}, "components": {"schemas": {"ApiResultOfBoolean": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "boolean"}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "ApiResultOfGuid": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "string", "format": "guid"}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "ApiResultOfGetOtpResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GetOtpResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetOtpResponse": {"type": "object", "additionalProperties": false, "properties": {"otpSendDate": {"type": "string", "format": "date-time"}, "otpExpirationDate": {"type": "string", "format": "date-time"}}}, "ApiResultOfListOfBannerResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/BannerResponse"}}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "BannerResponse": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "title": {"type": "string"}, "subTitle": {"type": "string"}, "imageUrl": {"type": "string"}, "linkText": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}}}, "ApiResultOfCaptchaTypeEnum": {"type": "object", "additionalProperties": false, "properties": {"data": {"$ref": "#/components/schemas/CaptchaTypeEnum"}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "CaptchaTypeEnum": {"type": "integer", "description": "", "x-enumNames": ["None", "GoogleRecaptchaV3", "Text"], "enum": [0, 100, 200]}, "ApiResultOfRequestCaptchaOutputDTO": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/RequestCaptchaOutputDTO"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "RequestCaptchaOutputDTO": {"type": "object", "additionalProperties": false, "properties": {"captchaImage": {"type": "string"}, "captchaSecretKey": {"type": "string"}}}, "CheckActorUserPasswordQuery": {"type": "object", "additionalProperties": false, "properties": {"password": {"type": "string"}}}, "ChangeActorUserPasswordCommand": {"type": "object", "additionalProperties": false, "properties": {"oldPassword": {"type": "string"}, "newPassword": {"type": "string"}, "confirmNewPassword": {"type": "string"}}}, "ApiResultOfListOfFinancialInstituteInfoResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/FinancialInstituteInfoResponse"}}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "FinancialInstituteInfoResponse": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "symbolName": {"type": "string"}, "fullName": {"type": "string"}, "logo": {"type": "string", "nullable": true}, "financialInstituteType": {"$ref": "#/components/schemas/FinancialInstituteTypeEnum"}, "fundConfig": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/FundConfig"}]}}}, "FinancialInstituteTypeEnum": {"type": "integer", "description": "", "x-enumNames": ["None", "Fund"], "enum": [0, 100]}, "FundConfig": {"type": "object", "additionalProperties": false, "properties": {"fundType": {"$ref": "#/components/schemas/FundTypeEnum"}, "fundsPageTags": {"type": "array", "items": {"type": "string"}}, "detailPageTags": {"type": "array", "items": {"type": "string"}}}}, "FundTypeEnum": {"type": "integer", "description": "", "x-enumNames": ["None", "FixedIncome"], "enum": [0, 100]}, "ApiResultOfGetOtpWithSecretKeyResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GetOtpWithSecretKeyResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetOtpWithSecretKeyResponse": {"type": "object", "additionalProperties": false, "properties": {"otpSendDate": {"type": "string", "format": "date-time"}, "otpExpirationDate": {"type": "string", "format": "date-time"}, "secretKey": {"type": "string", "format": "guid"}}}, "ForgetPasswordSendOTPCommand": {"type": "object", "additionalProperties": false, "properties": {"nationalCode": {"type": "string"}}}, "ForgetPasswordCheckOTPCommand": {"type": "object", "additionalProperties": false, "properties": {"secretKey": {"type": "string", "format": "guid"}, "otp": {"type": "string"}}}, "ForgetPasswordSetNewPasswordCommand": {"type": "object", "additionalProperties": false, "properties": {"secretKey": {"type": "string", "format": "guid"}, "newPassword": {"type": "string"}, "confirmNewPassword": {"type": "string"}}}, "ApiResultOfGetNAVsResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GetNAVsResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetNAVsResponse": {"type": "object", "additionalProperties": false, "properties": {"lastUpdate": {"type": "string", "format": "date-time", "nullable": true}, "navPurchase": {"type": "integer", "format": "int64"}, "minimumeNAVForPurchase": {"type": "integer", "format": "int64"}, "navSale": {"type": "integer", "format": "int64"}}}, "SendIssuanceOrderOTPCommand": {"type": "object", "additionalProperties": false, "properties": {"fundId": {"type": "string", "format": "guid"}}}, "ApiResultOfNullableGuid": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "string", "format": "guid", "nullable": true}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "ApiResultOfCreateIssuanceOrderByIPGResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/CreateIssuanceOrderByIPGResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "CreateIssuanceOrderByIPGResponse": {"type": "object", "additionalProperties": false, "properties": {"isSuccess": {"type": "boolean"}, "redirectUrl": {"type": "string", "nullable": true}}}, "CreateIssuanceOrderByIPGCommand": {"type": "object", "additionalProperties": false, "required": ["fundId", "rayanIPGId", "orderAmount"], "properties": {"fundId": {"type": "string", "format": "guid", "minLength": 1}, "rayanIPGId": {"type": "integer", "format": "int64", "maximum": **********.0, "minimum": 0.0}, "orderAmount": {"type": "integer", "format": "int64", "maximum": 9.22337203685478e+18, "minimum": 1.0}}}, "ApiResultOfListOfFundIPGBankResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/FundIPGBankResponse"}}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "FundIPGBankResponse": {"type": "object", "additionalProperties": false, "properties": {"rayanPaymentBankId": {"type": "integer", "format": "int64", "nullable": true}, "bankName": {"type": "string"}}}, "ApiResultOfListOfFundBankAccountResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/FundBankAccountResponse"}}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "FundBankAccountResponse": {"type": "object", "additionalProperties": false, "properties": {"rayanBankAccountId": {"type": "integer", "format": "int64", "nullable": true}, "bankName": {"type": "string", "nullable": true}, "bankAccountTypeName": {"type": "string", "nullable": true}, "accountNumber": {"type": "string", "nullable": true}, "branchName": {"type": "string", "nullable": true}}}, "SendRevokeOrderOTPCommand": {"type": "object", "additionalProperties": false, "properties": {"fundId": {"type": "string", "format": "guid"}}}, "CreateRevokeOrderCommand": {"type": "object", "additionalProperties": false, "required": ["fundId", "licenseNumber", "units", "customerBankShabaNumber", "otp"], "properties": {"fundId": {"type": "string", "format": "guid", "minLength": 1}, "licenseNumber": {"type": "integer", "format": "int64", "maximum": **********.0, "minimum": 1.0}, "units": {"type": "integer", "format": "int64", "maximum": **********.0, "minimum": 1.0}, "comments": {"type": "string", "nullable": true}, "customerBankShabaNumber": {"type": "string", "minLength": 1}, "otp": {"type": "string", "minLength": 1}}}, "ApiResultOfGetLoggedInUserProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GetLoggedInUserProfileResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetLoggedInUserProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"roles": {"type": "array", "items": {"$ref": "#/components/schemas/UserRoleProfileResponse"}}, "userProfile": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/UserProfileResponse"}]}, "visitorUserProfile": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/VisitorUserProfileResponse"}]}}}, "UserRoleProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"role": {"$ref": "#/components/schemas/RoleEnum"}, "roleName": {"type": "string"}, "roleDescription": {"type": "string"}}}, "RoleEnum": {"type": "integer", "description": "", "x-enumNames": ["None", "Administrator", "Visitor", "Customer", "FundCustomer"], "enum": [0, 100, 200, 300, 400]}, "UserProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string", "format": "guid"}, "createDate": {"type": "string", "format": "date-time"}, "isActive": {"type": "boolean"}, "userName": {"type": "string"}, "mobileNumber": {"type": "string"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "nationalCode": {"type": "string", "nullable": true}, "bourseCode": {"type": "string", "nullable": true}, "sejamProfiles": {"type": "array", "items": {"$ref": "#/components/schemas/UserSejamProfileResponse"}}}}, "UserSejamProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "createDate": {"type": "string", "format": "date-time"}, "userId": {"type": "string", "format": "guid"}, "financialInstituteId": {"type": "string", "format": "guid"}, "rayanCustomerId": {"type": "integer", "format": "int64", "nullable": true}, "bourseCode": {"type": "string"}, "nationalCode": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "fatherName": {"type": "string"}, "birthDate": {"type": "string", "format": "date-time", "nullable": true}, "birthPlace": {"type": "string"}, "birthCertNumber": {"type": "string"}, "birthCertificationId": {"type": "string"}, "phoneNumber": {"type": "string"}, "mobileNumber": {"type": "string"}, "email": {"type": "string", "nullable": true}, "address": {"type": "string"}, "postalCode": {"type": "string"}, "bankAccounts": {"type": "array", "items": {"$ref": "#/components/schemas/UserSejamBankAccountResponse"}}}}, "UserSejamBankAccountResponse": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "userSejamProfileId": {"type": "string", "format": "guid"}, "rayanBankAccountId": {"type": "integer", "format": "int64", "nullable": true}, "accountNumber": {"type": "string"}, "bankTypeName": {"type": "string"}, "bankName": {"type": "string"}, "bankBranch": {"type": "string"}, "bankBranchCode": {"type": "string"}, "shabaNumber": {"type": "string"}, "isDefault": {"type": "boolean"}}}, "VisitorUserProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"visitorUserId": {"type": "string", "format": "guid"}, "createDate": {"type": "string", "format": "date-time"}, "mobileNumber": {"type": "string"}}}, "ApiResultOfPromotionResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/PromotionResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "PromotionResponse": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "title": {"type": "string"}, "subTitle": {"type": "string"}, "imageUrl": {"type": "string"}, "linkText": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}}, "IsSejamiQuery": {"type": "object", "additionalProperties": false, "properties": {"financialInstituteId": {"type": "string", "format": "guid"}, "nationalCode": {"type": "string"}}}, "KycOTPCommand": {"type": "object", "additionalProperties": false, "properties": {"financialInstituteId": {"type": "string", "format": "guid"}, "nationalCode": {"type": "string"}}}, "ProfileInquiryCommand": {"type": "object", "additionalProperties": false, "properties": {"financialInstituteId": {"type": "string", "format": "guid"}, "nationalCode": {"type": "string"}, "kycOtpCode": {"type": "string"}}}, "ApiResultOfSejamProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/SejamProfileResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "SejamProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"rayanCustomerId": {"type": "integer", "format": "int64", "nullable": true}, "bourseCode": {"type": "string"}, "nationalCode": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "fatherName": {"type": "string"}, "birthDate": {"type": "string", "format": "date-time", "nullable": true}, "birthPlace": {"type": "string"}, "birthCertNumber": {"type": "string"}, "birthCertificationId": {"type": "string"}, "phoneNumber": {"type": "string"}, "mobileNumber": {"type": "string"}, "email": {"type": "string", "nullable": true}, "address": {"type": "string"}, "postalCode": {"type": "string"}, "bankAccounts": {"type": "array", "items": {"$ref": "#/components/schemas/SejamProfileBankAccountResponse"}}}}, "SejamProfileBankAccountResponse": {"type": "object", "additionalProperties": false, "properties": {"rayanBankAccountId": {"type": "integer", "format": "int64", "nullable": true}, "accountNumber": {"type": "string"}, "bankTypeName": {"type": "string"}, "bankName": {"type": "string"}, "bankBranch": {"type": "string"}, "bankBranchCode": {"type": "string"}, "shabaNumber": {"type": "string"}, "isDefault": {"type": "boolean"}}}, "SetAsCustomerProfileCommand": {"type": "object", "additionalProperties": false, "properties": {"financialInstituteId": {"type": "string", "format": "guid"}, "secretKey": {"type": "string", "format": "guid"}, "password": {"type": "string"}, "confirmPassword": {"type": "string"}}}, "UpdateProfileCommand": {"type": "object", "additionalProperties": false, "properties": {"financialInstituteId": {"type": "string", "format": "guid"}, "secretKey": {"type": "string", "format": "guid"}}}, "EditCurrentUserFrontendConfigCommand": {"type": "object", "additionalProperties": false, "properties": {"content": {"type": "string"}}}, "ApiResultOfString": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "string", "nullable": true}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "ApiResultOfGetAggregatedReturnOfInstituteComparedToBankResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GetAggregatedReturnOfInstituteComparedToBankResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetAggregatedReturnOfInstituteComparedToBankResponse": {"type": "object", "additionalProperties": false, "properties": {"instituteReturns": {"type": "array", "items": {"$ref": "#/components/schemas/AggregatedReturnDto"}}, "bankReturns": {"type": "array", "items": {"$ref": "#/components/schemas/AggregatedReturnDto"}}}}, "AggregatedReturnDto": {"type": "object", "additionalProperties": false, "properties": {"date": {"type": "string", "format": "date-time"}, "return": {"type": "number", "format": "decimal"}}}, "ApiResultOfGetFundOrderInfoByIdResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GetFundOrderInfoByIdResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetFundOrderInfoByIdResponse": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "createDate": {"type": "string", "format": "date-time"}, "fundId": {"type": "string", "format": "guid"}, "fundName": {"type": "string"}, "fundOrderType": {"$ref": "#/components/schemas/FundOrderTypeEnum"}, "paymentType": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/PaymentTypeEnum"}]}, "fundOrderIssuanceByBankReceipt": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/FundOrderIssuanceByBankReceiptInfoResponse"}]}, "fundOrderIssuanceByIPG": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/FundOrderIssuanceByIPGInfoResponse"}]}, "fundOrderRevoke": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/FundOrderRevokeInfoResponse"}]}}}, "FundOrderTypeEnum": {"type": "integer", "description": "", "x-enumNames": ["None", "Purchase", "Revoke"], "enum": [0, 100, 200]}, "PaymentTypeEnum": {"type": "integer", "description": "", "x-enumNames": ["None", "BankReceipt", "OnlineIPG", "POS"], "enum": [0, 100, 200, 300]}, "FundOrderIssuanceByBankReceiptInfoResponse": {"type": "object", "additionalProperties": false, "properties": {"bankAccountName": {"type": "string"}, "bankAccountNumber": {"type": "string"}, "orderAmount": {"type": "integer", "format": "int64"}, "receiptNumber": {"type": "string"}, "receiptDate": {"type": "string", "format": "date-time"}, "receiptComments": {"type": "string"}, "navPurchase": {"type": "integer", "format": "int64"}, "minimumeNAVForPurchase": {"type": "integer", "format": "int64"}}}, "FundOrderIssuanceByIPGInfoResponse": {"type": "object", "additionalProperties": false, "properties": {"updateDate": {"type": "string", "format": "date-time", "nullable": true}, "ipgName": {"type": "string"}, "orderAmount": {"type": "integer", "format": "int64"}, "navPurchase": {"type": "integer", "format": "int64"}, "minimumeNAVForPurchase": {"type": "integer", "format": "int64"}, "paymentSuccess": {"type": "boolean", "nullable": true}, "receiptNumber": {"type": "string", "nullable": true}}}, "FundOrderRevokeInfoResponse": {"type": "object", "additionalProperties": false, "properties": {"licenseNumber": {"type": "integer", "format": "int64"}, "units": {"type": "integer", "format": "int64"}, "comments": {"type": "string"}, "customerBankName": {"type": "string"}, "customerBankAccountNumber": {"type": "string"}, "customerBankShabaNumber": {"type": "string"}, "navSale": {"type": "integer", "format": "int64"}}}, "ApiResultOfPagingOutputOfGetInvestorOrdersResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/PagingOutputOfGetInvestorOrdersResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "PagingOutputOfGetInvestorOrdersResponse": {"type": "object", "additionalProperties": false, "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/GetInvestorOrdersResponse"}}, "totalCount": {"type": "integer", "format": "int64"}}}, "GetInvestorOrdersResponse": {"type": "object", "additionalProperties": false, "properties": {"rayanFundOrderId": {"type": "integer", "format": "int64", "nullable": true}, "unitPrice": {"type": "integer", "format": "int64", "nullable": true}, "orderAmount": {"type": "integer", "format": "int64", "nullable": true}, "rayanFoStatusId": {"type": "integer", "format": "int64", "nullable": true}, "fundOrderStatus": {"$ref": "#/components/schemas/FundOrderStatusEnum"}, "fundOrderStatusName": {"type": "string", "nullable": true}, "rayanFundOrderNumber": {"type": "string", "nullable": true}, "fundUnit": {"type": "integer", "format": "int64", "nullable": true}, "orderDate": {"type": "string", "format": "date-time", "nullable": true}, "creationDate": {"type": "string", "format": "date-time", "nullable": true}, "creationTime": {"type": "string", "nullable": true}, "modificationDate": {"type": "string", "format": "date-time", "nullable": true}, "modificationTime": {"type": "string", "nullable": true}, "licenseDate": {"type": "string", "format": "date-time", "nullable": true}, "orderPaymentTypeName": {"type": "string", "nullable": true}, "receiptNumber": {"type": "string", "nullable": true}, "receiptDate": {"type": "string", "format": "date-time", "nullable": true}, "fundName": {"type": "string", "nullable": true}, "isPurchase": {"type": "boolean"}, "fundOrderType": {"$ref": "#/components/schemas/FundOrderTypeEnum"}, "fundOrderTypeName": {"type": "string"}, "baseOrderDate": {"type": "string", "format": "date-time", "nullable": true}, "licenseNumber": {"type": "string", "nullable": true}}}, "FundOrderStatusEnum": {"type": "integer", "description": "", "x-enumNames": ["Unknown", "InProgress", "Accepted", "Rejected"], "enum": [0, 100, 200, 300]}, "ApiResultOfPagingOutputOfGetInvestorStatementsResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/PagingOutputOfGetInvestorStatementsResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "PagingOutputOfGetInvestorStatementsResponse": {"type": "object", "additionalProperties": false, "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/GetInvestorStatementsResponse"}}, "totalCount": {"type": "integer", "format": "int64"}}}, "GetInvestorStatementsResponse": {"type": "object", "additionalProperties": false, "properties": {"transactionDate": {"type": "string", "format": "date-time", "nullable": true}, "transactionType": {"type": "string", "nullable": true}, "comments": {"type": "string", "nullable": true}, "debitAmount": {"type": "integer", "format": "int64", "nullable": true}, "creditAmount": {"type": "integer", "format": "int64", "nullable": true}}}, "ApiResultOfListOfGetInvestorFundLicensesResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/GetInvestorFundLicensesResponse"}}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetInvestorFundLicensesResponse": {"type": "object", "additionalProperties": false, "properties": {"rayanFundLicenseId": {"type": "integer", "format": "int64", "nullable": true}, "licenseNumber": {"type": "integer", "format": "int64", "nullable": true}, "fundUnit": {"type": "integer", "format": "int64", "nullable": true}, "licenseDate": {"type": "string", "format": "date-time", "nullable": true}, "isCancelled": {"type": "boolean"}, "isValidLicense": {"type": "boolean"}}}, "ApiResultOfGetInvestorFundRemainsResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GetInvestorFundRemainsResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetInvestorFundRemainsResponse": {"type": "object", "additionalProperties": false, "properties": {"fundUnit": {"type": "integer", "format": "int64", "nullable": true}, "asset": {"type": "integer", "format": "int64", "nullable": true}}}, "ApiResultOfListOfGetInvestorAssetHistoryResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/GetInvestorAssetHistoryResponse"}}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetInvestorAssetHistoryResponse": {"type": "object", "additionalProperties": false, "properties": {"date": {"type": "string", "format": "date-time", "nullable": true}, "asset": {"type": "integer", "format": "int64", "nullable": true}}}, "ApiResultOfGetInvestorConsolidatedFundAssetReportResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GetInvestorConsolidatedFundAssetReportResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetInvestorConsolidatedFundAssetReportResponse": {"type": "object", "additionalProperties": false, "properties": {"sumAllUnit": {"type": "integer", "format": "int64"}, "sumAllAsset": {"type": "integer", "format": "int64"}, "fundAssets": {"type": "array", "items": {"$ref": "#/components/schemas/InvestorFundAssetDTO"}}}}, "InvestorFundAssetDTO": {"type": "object", "additionalProperties": false, "properties": {"fundId": {"type": "string", "format": "guid"}, "fundName": {"type": "string"}, "unit": {"type": "integer", "format": "int64"}, "asset": {"type": "integer", "format": "int64"}, "unitPercentOfAll": {"type": "number", "format": "double"}, "assetPercentOfAll": {"type": "number", "format": "double"}}}, "ApiResultOfGetInvestorConsolidatedRecentFundOrdersReportResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GetInvestorConsolidatedRecentFundOrdersReportResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetInvestorConsolidatedRecentFundOrdersReportResponse": {"type": "object", "additionalProperties": false, "properties": {"issuances": {"type": "array", "items": {"$ref": "#/components/schemas/RequestOrderDTO"}}, "revokes": {"type": "array", "items": {"$ref": "#/components/schemas/RequestOrderDTO"}}}}, "RequestOrderDTO": {"type": "object", "additionalProperties": false, "properties": {"status": {"$ref": "#/components/schemas/FundOrderStatusEnum"}, "count": {"type": "integer", "format": "int64"}}}, "ApiResultOfGetFundTotalNetAssetValueResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GetFundTotalNetAssetValueResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetFundTotalNetAssetValueResponse": {"type": "object", "additionalProperties": false, "properties": {"fundTotalNetAssetValue": {"type": "number", "format": "decimal"}, "reportDate": {"type": "string", "format": "date-time"}}}, "ApiResultOfGetFundNetAssetValueWithChangesResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GetFundNetAssetValueWithChangesResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetFundNetAssetValueWithChangesResponse": {"type": "object", "additionalProperties": false, "properties": {"saleNav": {"type": "integer", "format": "int64"}, "purchaseNav": {"type": "integer", "format": "int64"}, "saleNavChangePercent": {"type": "number", "format": "decimal"}, "purchaseNavChangePercent": {"type": "number", "format": "decimal"}}}, "ApiResultOfGetFundNetAssetValuesChartResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GetFundNetAssetValuesChartResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetFundNetAssetValuesChartResponse": {"type": "object", "additionalProperties": false, "properties": {"fundNetAssetValues": {"type": "array", "items": {"$ref": "#/components/schemas/FundNetAssetValuesChartDto"}}}}, "FundNetAssetValuesChartDto": {"type": "object", "additionalProperties": false, "properties": {"date": {"type": "string", "format": "date-time"}, "saleNav": {"type": "integer", "format": "int64"}, "purchaseNav": {"type": "integer", "format": "int64"}}}, "ApiResultOfListOfGetFundIndustriesSharePercentViewModel": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/GetFundIndustriesSharePercentViewModel"}}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetFundIndustriesSharePercentViewModel": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "sharePercent": {"type": "number", "format": "decimal"}, "viewIndex": {"type": "integer", "format": "int32"}, "reportDate": {"type": "string", "format": "date-time"}}}, "ApiResultOfListOfFundAssetCompositionViewModel": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/FundAssetCompositionViewModel"}}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "FundAssetCompositionViewModel": {"type": "object", "additionalProperties": false, "properties": {"assetCompositionType": {"$ref": "#/components/schemas/FundAssetCompositionTypeEnum"}, "title": {"type": "string"}, "assetCompositionPercent": {"type": "number", "format": "decimal"}, "assetCompositionValue": {"type": "number", "format": "decimal"}, "viewIndex": {"type": "integer", "format": "int32"}, "reportDate": {"type": "string", "format": "date-time"}}}, "FundAssetCompositionTypeEnum": {"type": "integer", "description": "", "x-enumNames": ["None", "Stock", "Bond", "<PERSON><PERSON><PERSON><PERSON>", "GoodsDepositCertificate", "Fund", "Other", "Cash"], "enum": [0, 100, 200, 300, 400, 600, 700, 800]}, "ApiResultOfGetFundTimePeriodBasedSimpleReturnsResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GetFundTimePeriodBasedSimpleReturnsResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetFundTimePeriodBasedSimpleReturnsResponse": {"type": "object", "additionalProperties": false, "properties": {"monthlySimpleReturn": {"type": "number", "format": "decimal"}, "threeMonthsSimpleReturn": {"type": "number", "format": "decimal"}, "sixMonthsSimpleReturn": {"type": "number", "format": "decimal"}, "yearlySimpleReturn": {"type": "number", "format": "decimal"}}}, "CreateBannerCommand": {"type": "object", "additionalProperties": false, "properties": {"title": {"type": "string"}, "subTitle": {"type": "string"}, "imageUrl": {"type": "string"}, "linkText": {"type": "string"}, "sortOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}}}, "UpdateBannerCommand": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "title": {"type": "string"}, "subTitle": {"type": "string"}, "imageUrl": {"type": "string"}, "linkText": {"type": "string"}, "sortOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}}}, "DeleteBannerCommand": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}}}, "ApiResultOfBannerResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/BannerResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "ApiResultOfPagingOutputOfBannerResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/PagingOutputOfBannerResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "PagingOutputOfBannerResponse": {"type": "object", "additionalProperties": false, "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/BannerResponse"}}, "totalCount": {"type": "integer", "format": "int64"}}}, "ApiResultOfBannersCountResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/BannersCountResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "BannersCountResponse": {"type": "object", "additionalProperties": false, "properties": {"totalCount": {"type": "integer", "format": "int32"}, "activeCount": {"type": "integer", "format": "int32"}}}, "CreatePromotionCommand": {"type": "object", "additionalProperties": false, "properties": {"title": {"type": "string"}, "subTitle": {"type": "string"}, "imageUrl": {"type": "string"}, "linkText": {"type": "string"}, "isActive": {"type": "boolean"}}}, "UpdatePromotionCommand": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "title": {"type": "string"}, "subTitle": {"type": "string"}, "imageUrl": {"type": "string"}, "linkText": {"type": "string"}, "isActive": {"type": "boolean"}}}, "DeletePromotionCommand": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}}}, "ApiResultOfPagingOutputOfPromotionResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/PagingOutputOfPromotionResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "PagingOutputOfPromotionResponse": {"type": "object", "additionalProperties": false, "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/PromotionResponse"}}, "totalCount": {"type": "integer", "format": "int64"}}}, "ApiResultOfSystemSettingsResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/SystemSettingsResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "SystemSettingsResponse": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "captchaType": {"$ref": "#/components/schemas/CaptchaTypeEnum"}}}, "EditSystemSettingsCommand": {"type": "object", "additionalProperties": false, "properties": {"captchaTypeEnum": {"$ref": "#/components/schemas/CaptchaTypeEnum"}}}, "ApiResultOfPagingOutputOfCaptchaSettingsHistoryResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/PagingOutputOfCaptchaSettingsHistoryResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "PagingOutputOfCaptchaSettingsHistoryResponse": {"type": "object", "additionalProperties": false, "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/CaptchaSettingsHistoryResponse"}}, "totalCount": {"type": "integer", "format": "int64"}}}, "CaptchaSettingsHistoryResponse": {"type": "object", "additionalProperties": false, "properties": {"captchaType": {"$ref": "#/components/schemas/CaptchaTypeEnum"}, "actionDate": {"type": "string", "format": "date-time"}}}}}}