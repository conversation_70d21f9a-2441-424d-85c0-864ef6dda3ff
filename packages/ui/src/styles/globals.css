@import "./variables.css";
@import "./tailwind-theme.css";
@import "tailwindcss";
@source "../../../apps/**/*.{ts,tsx}";
@source "../../../components/**/*.{ts,tsx}";
@source "../**/*.{ts,tsx}";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@layer base {
  * {
    @apply border-border-nautral-default outline-border-nautral-default/50;
  }
  body {
    @apply bg-background-nautral-body text-text-nautral-default;
  }
}

/** hide browser background color when autofill */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
  box-shadow: 0 0 0 1000px transparent inset !important;
  -webkit-text-fill-color: inherit !important;
  background-color: transparent !important;
  transition: background-color 99999s ease-in-out 0s;
}

.highcharts-container {
  width: auto !important;
  height: auto !important;
}

.highcharts-root {
  width: 100% !important;
  height: 100% !important;
}
