// Helper function to format number with commas
function commaSeparator(value: string): string {
  if (!value) return "";

  // Remove any existing commas
  const plainNumber = value.replace(/,/g, "");

  // Handle decimal numbers
  const parts = plainNumber.split(".");
  const integerPart = parts[0] || "";
  const decimalPart = parts.length > 1 ? `.${parts[1]}` : "";

  // Add commas for thousands separator (only to the integer part)
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  // Combine integer and decimal parts
  return `${formattedInteger}${decimalPart}`;
}
export default commaSeparator;

export function toDecimals(
  value?: number,
  decimalPlaces = 2,
  roundTheDecimals = true,
): string {
  if (roundTheDecimals) {
    // number with decimals with rounding last digit
    return value?.toFixed(decimalPlaces)?.replace(/\.00$/, "") as string;
  }
  // number with decimals without rounding
  return (Math.floor(Number(value) * 10 ** decimalPlaces) / 10 ** decimalPlaces)
    .toFixed(decimalPlaces)
    ?.replace(/\.00$/, "");
}

export function formatNumber(
  value?: number,
  decimalPlaces = 2,
  roundTheDecimals = true,
): string {
  if (value === undefined || value === null || isNaN(value)) {
    return "";
  }

  let numberString: string;

  if (roundTheDecimals) {
    // Round to fixed decimal places
    numberString = value.toFixed(decimalPlaces);
  } else {
    // Truncate decimals without rounding
    const factor = 10 ** decimalPlaces;
    numberString = (Math.floor(value * factor) / factor).toFixed(decimalPlaces);
  }

  // Remove trailing .00 if present
  numberString = numberString.replace(/\.00$/, "");

  // Now apply commas on the integer part
  const parts = numberString.split(".");

  const integerPartWithCommas = (parts[0] || "").replace(
    /\B(?=(\d{3})+(?!\d))/g,
    ",",
  );

  if (parts.length > 1) {
    return `${integerPartWithCommas}.${parts[1]}`;
  }

  return integerPartWithCommas;
}
