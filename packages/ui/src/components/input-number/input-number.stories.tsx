import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { InputNumber } from "@workspace/ui/components/input-number";
import { useState } from "react";

const meta: Meta<typeof InputNumber> = {
  title: "UI/Input/InputNumber",
  component: InputNumber,
  parameters: {
    layout: "centered",
  },
  // tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="container max-w-sm p-8">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof InputNumber>;

export const Default: Story = {
  args: {
    title: "قیمت اسمی",
  },
};

export const WithDefaultValue: Story = {
  args: {
    title: "مبلغ نهایی",
    defaultValue: "1000",
  },
};

export const Controlled: Story = {
  render: () => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [value, setValue] = useState<string>("5000");

    console.log("value", value);

    return (
      <div className="flex flex-col gap-4">
        <InputNumber
          title="Enter a number"
          value={value}
          onChange={(e) => setValue(e.target.value)}
        />
        <div>value: {value}</div>
        <pre>(The value is raw (without commas))</pre>
      </div>
    );
  },
};
