import * as React from "react";
import { VariantProps } from "class-variance-authority";
import { filePickerVariants } from "@workspace/ui/components/file-picker/utils";

export interface FilePickerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof filePickerVariants> {
  /**
   * Callback function when files are selected
   */
  onFilesChange: (files: FileList | null) => void;
  /**
   * Accepted file formats (e.g., ".pdf,.doc,.docx")
   */
  accept?: string;
  /**
   * Whether multiple files can be selected
   */
  multiple?: boolean;
  /**
   * Whether the component is disabled
   */
  disabled?: boolean;
  /**
   * Text to display when no files are selected
   */
  placeholder?: string;
  /**
   * Additional className for the root element
   */
  rootClassName?: string;
  /**
   * Maximum file size in bytes
   */
  maxSize?: number;
  /**
   * icon
   */
  icon?: React.ReactNode;
  /**
   * Hint text to display below the box
   */
  hintText?: string;
}
