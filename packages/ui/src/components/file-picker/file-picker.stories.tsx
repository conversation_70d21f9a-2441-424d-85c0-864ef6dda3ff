import type { <PERSON>a, StoryObj } from "@storybook/react";
import { useState } from "react";
import { FilePicker } from "@workspace/ui/components/file-picker";

const meta: Meta<typeof FilePicker> = {
  title: "UI/FilePicker",
  component: FilePicker,
  parameters: {
    layout: "centered",
  },
  decorators: [
    (Story) => (
      <div className="container max-w-md p-8">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    variant: {
      options: ["default", "error", "success"],
      control: { type: "radio" },
    },
    size: {
      options: ["sm", "md", "lg"],
      control: { type: "radio" },
    },
    accept: {
      control: { type: "text" },
    },
    multiple: {
      control: { type: "boolean" },
    },
    disabled: {
      control: { type: "boolean" },
    },
    placeholder: {
      control: { type: "text" },
    },
    maxSize: {
      control: { type: "number" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof FilePicker>;

const FilePickerWithState = (args: any) => {
  const [files, setFiles] = useState<FileList | null>(null);

  const handleFilesChange = (selectedFiles: FileList | null) => {
    setFiles(selectedFiles);
  };

  return (
    <div className="space-y-4">
      <FilePicker {...args} onFilesChange={handleFilesChange} />
    </div>
  );
};

export const Default: Story = {
  args: {
    placeholder: "Drag and drop files here or click to browse",
    multiple: false,
  },
  render: (args) => <FilePickerWithState {...args} />,
};

export const ImageFiles: Story = {
  args: {
    placeholder: "Upload images only",
    accept: "image/*",
  },
  render: (args) => <FilePickerWithState {...args} />,
};

export const PdfFiles: Story = {
  args: {
    placeholder: "Upload PDF documents",
    accept: ".pdf",
  },
  render: (args) => <FilePickerWithState {...args} />,
};

export const MultipleFiles: Story = {
  args: {
    placeholder: "Upload multiple files",
    multiple: true,
    accept: ".pdf,.doc,.docx,.jpg,.png",
  },
  render: (args) => <FilePickerWithState {...args} />,
};

export const SmallSize: Story = {
  args: {
    placeholder: "Small file picker",
    size: "sm",
  },
  render: (args) => <FilePickerWithState {...args} />,
};

export const LargeSize: Story = {
  args: {
    placeholder: "Large file picker",
    size: "lg",
  },
  render: (args) => <FilePickerWithState {...args} />,
};

export const ErrorState: Story = {
  args: {
    placeholder: "File picker with error state",
    variant: "error",
  },
  render: (args) => <FilePickerWithState {...args} />,
};

export const SuccessState: Story = {
  args: {
    placeholder: "File picker with success state",
    variant: "success",
  },
  render: (args) => <FilePickerWithState {...args} />,
};

export const Disabled: Story = {
  args: {
    placeholder: "Disabled file picker",
    disabled: true,
  },
  render: (args) => <FilePickerWithState {...args} />,
};

export const MaxSize: Story = {
  args: {
    placeholder: "Maxsize file picker",
    accept: ".pdf,.doc,.docx,.jpg,.png",
    maxSize: 300000,
  },
  render: (args) => <FilePickerWithState {...args} />,
};
