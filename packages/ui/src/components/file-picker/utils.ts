import { cva } from "class-variance-authority";

export const filePickerVariants = cva(
  "relative flex flex-col items-center justify-center w-full h-full border-2 border-dashed rounded-lg transition-colors cursor-pointer",
  {
    variants: {
      variant: {
        default:
          "border-border-nautral-default hover:border-border-primary-default",
        error: "border-border-error-default",
        success: "border-border-success-default",
      },
      size: {
        sm: "p-2 min-h-[80px]",
        md: "p-4 min-h-[120px]",
        lg: "p-6 min-h-[160px]",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  },
);

// Helper function to create a FileList with a single file
export const createFileListWithSingleFile = (file: File): FileList => {
  const dataTransfer = new DataTransfer();
  dataTransfer.items.add(file);
  return dataTransfer.files;
};
