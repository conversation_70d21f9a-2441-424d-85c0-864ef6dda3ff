import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { defaultColDefs } from "./columnDefs";
import { AgGrid } from "@workspace/ui/components/ag-grid";
import { IPerson } from "@workspace/ui/components/ag-grid/stories/data";

const meta: Meta<typeof AgGrid> = {
  title: "UI/AgGrid/Empty",
  component: AgGrid,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component: "AG Grid with empty state - no data to display.",
      },
    },
  },
  decorators: [
    (Story) => (
      <div
        style={{
          height: "400px",
          width: "100%",
          overflow: "hidden",
          padding: 20,
          direction: "rtl",
        }}
        dir="rtl"
      >
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof AgGrid<IPerson>>;

export const Empty: Story = {
  args: {
    rowData: [],
    columnDefs: defaultColDefs,
    animateRows: true,
  },
};
