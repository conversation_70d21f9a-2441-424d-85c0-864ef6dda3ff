import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { AgGrid } from "@workspace/ui/components/ag-grid";
import { defaultColDefs } from "@workspace/ui/components/ag-grid/stories/columnDefs";
import {
  sampleData,
  IPerson,
} from "@workspace/ui/components/ag-grid/stories/data";

const meta: Meta<typeof AgGrid> = {
  title: "UI/AgGrid/Basic",
  component: AgGrid,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component: "Basic AG Grid implementation with sample data.",
      },
    },
  },
  decorators: [
    (Story) => (
      <div
        style={{
          height: "400px",
          width: "100%",
          overflow: "hidden",
          padding: 20,
          direction: "rtl",
        }}
        dir="rtl"
      >
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof AgGrid<IPerson>>;

export const Basic: Story = {
  args: {
    rowData: sampleData,
    columnDefs: defaultColDefs,
    animateRows: true,
  },
};
