import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ColDef } from "ag-grid-community";

import { defaultColDefs } from "./columnDefs";
import { AgGrid } from "@workspace/ui/components/ag-grid";
import {
  IPerson,
  sampleData,
} from "@workspace/ui/components/ag-grid/stories/data";

const meta: Meta<typeof AgGrid> = {
  title: "UI/AgGrid/CustomStyling",
  component: AgGrid,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "AG Grid with custom cell and row styling based on data values.",
      },
    },
  },
  decorators: [
    (Story) => (
      <div
        style={{
          height: "400px",
          width: "100%",
          overflow: "hidden",
          padding: 20,
          direction: "rtl",
        }}
        dir="rtl"
      >
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof AgGrid>;

export const CustomStyling: Story = {
  args: {
    rowData: sampleData,
    columnDefs: [
      ...defaultColDefs,
      {
        field: "status" as any,
        headerName: "Status",
        width: 100,
        cellStyle: (params: any) => {
          const data = params.data as IPerson;
          if (data && data.salary > 70000) {
            return { backgroundColor: "#dcfce7", color: "#166534" };
          }
          return { backgroundColor: "#fef3c7", color: "#92400e" };
        },
      },
    ] as ColDef[],
    animateRows: true,
    getRowStyle: (params: any) => {
      const data = params.data as IPerson;
      if (data && data.age > 35) {
        return { fontWeight: "bold" };
      }
      return undefined;
    },
  },
};
