import { <PERSON>erson } from "@workspace/ui/components/ag-grid/stories/data";
import { TableColDef } from "@workspace/ui/components/ag-grid/types";

export const defaultColDefs: TableColDef<IPerson>[] = [
  {
    field: "id",
    headerName: "ردیف",
    width: 80,
    sortable: true,
    filter: true,
  },
  {
    field: "name",
    headerName: "Name",
    width: 150,
    sortable: true,
    filter: true,
  },
  {
    field: "age",
    headerName: "Age",
    width: 80,
    sortable: true,
    filter: true,
    type: "numericColumn",
  },
  {
    field: "email",
    headerName: "Email",
    width: 200,
    sortable: true,
    filter: true,
  },
  {
    field: "country",
    headerName: "Country",
    width: 120,
    sortable: true,
    filter: true,
  },
  {
    field: "salary",
    headerName: "Salary",
    width: 120,
    sortable: true,
    filter: true,
    type: "numericColumn",
    valueFormatter: (params) =>
      `$${(params.value as number)?.toLocaleString() || 0}`,
  },
  {
    field: "department",
    headerName: "Department",
    width: 130,
    sortable: true,
    filter: true,
  },
  {
    field: "joinDate",
    headerName: "Join Date",
    width: 120,
    sortable: true,
    filter: true,
    type: "dateColumn",
  },
];
