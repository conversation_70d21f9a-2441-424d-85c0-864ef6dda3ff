import { GridOptions, ColGroupDef, ColDef } from "ag-grid-community";
import { AgGridReact, AgGridReactProps } from "ag-grid-react";

export type TTableRef<TData> = {
  agGridRef: AgGridReact<TData> | null;
};

export interface ITableProps<TData> extends AgGridReactProps<TData> {}
export interface TableColDef<TData> extends ColDef<TData> {}
export interface TableGridOptions<TData> extends GridOptions<TData> {}
export interface TableColGroupDef<TData> extends ColGroupDef<TData> {}
