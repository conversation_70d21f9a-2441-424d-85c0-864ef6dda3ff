import { themeQuartz } from "ag-grid-community";

// to use myTheme in an application, pass it to the theme grid option
export const myTheme = themeQuartz.withParams({
  backgroundColor: "var(--surface-nautral-default-3)",
  oddRowBackgroundColor: "var(--surface-nautral-default-4)",
  rowHoverColor: "var(--surface-nautral-default-2)",
  browserColorScheme: "dark",
  textColor: "var(--text-nautral-secondary)",
  chromeBackgroundColor: {
    ref: "foregroundColor",
    mix: 0.07,
    onto: "backgroundColor",
  },
  fontFamily: "inherit",
  fontSize: 16,
  foregroundColor: "var(--border-nautral-default)",
  headerBackgroundColor: "var(--surface-nautral-default-4)",
  headerFontSize: 14,
  headerTextColor: "var(--text-nautral-default)",
  rowBorder: false,
});
