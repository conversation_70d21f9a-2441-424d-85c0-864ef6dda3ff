import React, { useImperativeHandle, forwardRef, useRef, Ref } from "react";
import { AgGridReact } from "ag-grid-react";
import { ModuleRegistry, AllCommunityModule } from "ag-grid-community";
import { ITableProps, TTableRef } from "./types";
import { myTheme } from "@workspace/ui/components/ag-grid/theme";
import { NoDataOverlay } from "@workspace/ui/components/ag-grid/utils";

ModuleRegistry.registerModules([AllCommunityModule]);

const TableInner = <TData,>(
  props: ITableProps<TData>,
  ref: Ref<TTableRef<TData>>,
) => {
  const agGridRef = useRef<AgGridReact<TData>>(null);

  useImperativeHandle(ref, () => ({
    agGridRef: agGridRef.current,
  }));

  return (
    <AgGridReact<TData>
      {...props}
      ref={agGridRef}
      theme={myTheme}
      noRowsOverlayComponent={props?.noRowsOverlayComponent || NoDataOverlay}
      // icons={{
      //   sortAscending: `<img src="/sort-selected.svg" class="sort-selected-icon" alt="svg"/>`,
      //   sortDescending: `<img src="/sort-selected.svg" class="sort-selected-icon" alt="svg"/>`,
      // }}
    />
  );
};

const Table = forwardRef(TableInner) as <T>(
  props: ITableProps<T> & React.RefAttributes<TTableRef<T>>,
) => React.JSX.Element;

export default Table;
