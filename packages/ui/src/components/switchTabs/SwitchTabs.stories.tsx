import type { <PERSON>a, StoryObj } from "@storybook/react";
import SwitchTabs from "@workspace/ui/components/switchTabs/SwitchTabs";

const meta: Meta<typeof SwitchTabs> = {
  component: SwitchTabs,
  title: "UI/SwitchTabs",
  decorators: [
    (Story) => (
      <div className="container max-w-sm p-8">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof SwitchTabs>;

// 👇 Throws a type error it the args don't match the component props
export const Default: Story = {
  args: {
    tabs: [
      { id: 1, label: "تب اول" },
      { id: 2, label: "تب دوم" },
      { id: 3, label: "تب سوم" },
    ],
    activeItemId: 2,
    onChange: (id) => {
      console.log("active tab: ", id);
    },
  },
};
