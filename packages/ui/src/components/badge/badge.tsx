import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@workspace/ui/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-3xl border px-[11.5px] py-[7px] text-xs font-normal w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow,border-color] overflow-hidden",
  {
    variants: {
      state: {
        default:
          "border-transparent bg-surface-nautral-default-1 text-text-nautral-secondary [a&]:hover:bg-surface-nautral-default-1/90",
        active:
          "border-border-primary-default bg-button-primary-default-2 text-text-primary-default [a&]:hover:bg-button-primary-default/90",
      },
    },
    defaultVariants: {
      state: "default",
    },
  },
);

function Badge({
  className,
  state,
  asChild = false,
  ...props
}: React.ComponentProps<"span"> &
  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {
  const Comp = asChild ? Slot : "span";

  return (
    <Comp
      data-slot="badge"
      className={cn(badgeVariants({ state }), className, "")}
      {...props}
    />
  );
}

export { Badge, badgeVariants };
