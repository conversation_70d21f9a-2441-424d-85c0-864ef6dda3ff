import {
  PaginationContent,
  PaginationEllipsis,
  <PERSON>ginationFirst,
  PaginationItem,
  PaginationLast,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationUi,
} from "@workspace/ui/components/pagination/paginationUi";
import { ISmartPaginationProps } from "@workspace/ui/components/pagination/types";

export function Pagination({
  totalCount,
  pageNumber,
  rowsPerPage,
  setPageNumber,
  className,
  maxVisiblePages = 5,
  showFirstLast = true, // Default to showing first/last buttons
}: ISmartPaginationProps) {
  // Calculate total pages
  const totalPages = Math.ceil(totalCount / rowsPerPage);

  // Don't render if there's only one page or no data
  if (totalPages <= 1) {
    return null;
  }

  // Calculate which pages to show
  const getVisiblePages = () => {
    const pages: (number | "ellipsis")[] = [];

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total pages is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      const startPage = Math.max(
        2,
        pageNumber - Math.floor(maxVisiblePages / 2),
      );
      const endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 3);

      // Add ellipsis after first page if needed
      if (startPage > 2) {
        pages.push("ellipsis");
      }

      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      // Add ellipsis before last page if needed
      if (endPage < totalPages - 1) {
        pages.push("ellipsis");
      }

      // Always show last page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const visiblePages = getVisiblePages();
  const canGoPrevious = pageNumber > 1;
  const canGoNext = pageNumber < totalPages;
  const canGoFirst = pageNumber > 1;
  const canGoLast = pageNumber < totalPages;

  const handleFirst = () => {
    if (canGoFirst) {
      setPageNumber(1);
    }
  };

  const handlePrevious = () => {
    if (canGoPrevious) {
      setPageNumber(pageNumber - 1);
    }
  };

  const handleNext = () => {
    if (canGoNext) {
      setPageNumber(pageNumber + 1);
    }
  };

  const handleLast = () => {
    if (canGoLast) {
      setPageNumber(totalPages);
    }
  };

  const handlePageClick = (page: number) => {
    setPageNumber(page);
  };

  return (
    <PaginationUi className={className}>
      <PaginationContent>
        {/* First Button */}
        {showFirstLast && (
          <PaginationItem>
            <PaginationFirst
              href="#"
              onClick={(e) => {
                e.preventDefault();
                handleFirst();
              }}
              style={{
                opacity: canGoFirst ? 1 : 0.5,
                pointerEvents: canGoFirst ? "auto" : "none",
                cursor: canGoFirst ? "pointer" : "default",
              }}
            />
          </PaginationItem>
        )}

        {/* Previous Button */}
        <PaginationItem>
          <PaginationPrevious
            href="#"
            onClick={(e) => {
              e.preventDefault();
              handlePrevious();
            }}
            style={{
              opacity: canGoPrevious ? 1 : 0.5,
              pointerEvents: canGoPrevious ? "auto" : "none",
              cursor: canGoPrevious ? "pointer" : "default",
            }}
          />
        </PaginationItem>

        {/* Page Numbers */}
        {visiblePages.map((page, index) => (
          <PaginationItem key={index}>
            {page === "ellipsis" ? (
              <PaginationEllipsis />
            ) : (
              <PaginationLink
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  handlePageClick(page);
                }}
                isActive={page === pageNumber}
                style={{ cursor: "pointer" }}
              >
                {page}
              </PaginationLink>
            )}
          </PaginationItem>
        ))}

        {/* Next Button */}
        <PaginationItem>
          <PaginationNext
            href="#"
            onClick={(e) => {
              e.preventDefault();
              handleNext();
            }}
            style={{
              opacity: canGoNext ? 1 : 0.5,
              pointerEvents: canGoNext ? "auto" : "none",
              cursor: canGoNext ? "pointer" : "default",
            }}
          />
        </PaginationItem>

        {/* Last Button */}
        {showFirstLast && (
          <PaginationItem>
            <PaginationLast
              href="#"
              onClick={(e) => {
                e.preventDefault();
                handleLast();
              }}
              style={{
                opacity: canGoLast ? 1 : 0.5,
                pointerEvents: canGoLast ? "auto" : "none",
                cursor: canGoLast ? "pointer" : "default",
              }}
            />
          </PaginationItem>
        )}
      </PaginationContent>
    </PaginationUi>
  );
}
