import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Pagination } from "@workspace/ui/components/pagination";
import { useState } from "react";

const meta: Meta<typeof Pagination> = {
  title: "UI/Pagination",
  component: Pagination,
  parameters: {
    layout: "centered",
  },
  decorators: [
    (Story) => (
      <div className="container max-w-4xl p-8">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    totalCount: {
      control: { type: "number", min: 1, max: 1000 },
      description: "Total number of items",
    },
    pageNumber: {
      control: { type: "number", min: 1 },
      description: "Current page number (1-based)",
    },
    rowsPerPage: {
      control: { type: "number", min: 1, max: 100 },
      description: "Number of items per page",
    },
    maxVisiblePages: {
      control: { type: "number", min: 3, max: 10 },
      description: "Maximum number of visible page buttons",
    },
    showFirstLast: {
      control: { type: "boolean" },
      description: "Whether to show first/last buttons",
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS classes",
    },
  },
};

export default meta;
type Story = StoryObj<typeof Pagination>;

// Basic pagination with static props
export const Basic: Story = {
  args: {
    totalCount: 100,
    pageNumber: 1,
    rowsPerPage: 10,
    maxVisiblePages: 5,
    showFirstLast: true,
    setPageNumber: () => {}, // No-op for static story
  },
  render: (args) => {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Basic Pagination</h3>
        <p className="text-sm text-gray-600">
          Static pagination showing 100 items with 10 per page (10 total pages).
        </p>
        <div className="rounded-lg bg-gray-100 p-4">
          <p className="text-sm">
            <strong>Total Items:</strong> {args.totalCount}
          </p>
          <p className="text-sm">
            <strong>Current Page:</strong> {args.pageNumber}
          </p>
          <p className="text-sm">
            <strong>Items per Page:</strong> {args.rowsPerPage}
          </p>
          <p className="text-sm">
            <strong>Total Pages:</strong>{" "}
            {Math.ceil(args.totalCount / args.rowsPerPage)}
          </p>
        </div>
        <Pagination {...args} />
      </div>
    );
  },
};

// Pagination without first/last buttons
export const WithoutFirstLast: Story = {
  args: {
    totalCount: 250,
    pageNumber: 5,
    rowsPerPage: 10,
    maxVisiblePages: 5,
    showFirstLast: false,
    setPageNumber: () => {},
  },
  render: (args) => {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Without First/Last Buttons</h3>
        <p className="text-sm text-gray-600">
          Pagination without first/last buttons (25 total pages, showing page
          5).
        </p>
        <div className="rounded-lg bg-gray-100 p-4">
          <p className="text-sm">
            <strong>Total Items:</strong> {args.totalCount}
          </p>
          <p className="text-sm">
            <strong>Current Page:</strong> {args.pageNumber}
          </p>
          <p className="text-sm">
            <strong>Items per Page:</strong> {args.rowsPerPage}
          </p>
          <p className="text-sm">
            <strong>Total Pages:</strong>{" "}
            {Math.ceil(args.totalCount / args.rowsPerPage)}
          </p>
        </div>
        <Pagination {...args} />
      </div>
    );
  },
};

// Pagination with fewer visible pages
export const FewVisiblePages: Story = {
  args: {
    totalCount: 500,
    pageNumber: 10,
    rowsPerPage: 10,
    maxVisiblePages: 3,
    showFirstLast: true,
    setPageNumber: () => {},
  },
  render: (args) => {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Few Visible Pages</h3>
        <p className="text-sm text-gray-600">
          Pagination with only 3 visible pages (50 total pages, showing page
          10).
        </p>
        <div className="rounded-lg bg-gray-100 p-4">
          <p className="text-sm">
            <strong>Total Items:</strong> {args.totalCount}
          </p>
          <p className="text-sm">
            <strong>Current Page:</strong> {args.pageNumber}
          </p>
          <p className="text-sm">
            <strong>Items per Page:</strong> {args.rowsPerPage}
          </p>
          <p className="text-sm">
            <strong>Total Pages:</strong>{" "}
            {Math.ceil(args.totalCount / args.rowsPerPage)}
          </p>
          <p className="text-sm">
            <strong>Max Visible:</strong> {args.maxVisiblePages}
          </p>
        </div>
        <Pagination {...args} />
      </div>
    );
  },
};

// Interactive controlled pagination
export const Interactive: Story = {
  args: {
    totalCount: 200,
    rowsPerPage: 10,
    maxVisiblePages: 5,
    showFirstLast: true,
  },
  render: (args) => {
    const InteractivePagination = () => {
      const [currentPage, setCurrentPage] = useState(1);
      const [itemsPerPage, setItemsPerPage] = useState(args.rowsPerPage);
      const [totalItems, setTotalItems] = useState(args.totalCount);

      const totalPages = Math.ceil(totalItems / itemsPerPage);

      return (
        <div className="space-y-6">
          <h3 className="text-lg font-semibold">Interactive Pagination</h3>
          <p className="text-sm text-gray-600">
            Fully interactive pagination. Try changing the values and clicking
            through pages.
          </p>

          <div className="grid grid-cols-1 gap-4 rounded-lg bg-gray-100 p-4 sm:grid-cols-3">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Total Items
              </label>
              <input
                type="number"
                value={totalItems}
                onChange={(e) => {
                  const value = Math.max(1, parseInt(e.target.value) || 1);
                  setTotalItems(value);
                  // Reset to page 1 if current page exceeds new total
                  const newTotalPages = Math.ceil(value / itemsPerPage);
                  if (currentPage > newTotalPages) {
                    setCurrentPage(1);
                  }
                }}
                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                min="1"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Items per Page
              </label>
              <input
                type="number"
                value={itemsPerPage}
                onChange={(e) => {
                  const value = Math.max(1, parseInt(e.target.value) || 1);
                  setItemsPerPage(value);
                  // Reset to page 1 if current page exceeds new total
                  const newTotalPages = Math.ceil(totalItems / value);
                  if (currentPage > newTotalPages) {
                    setCurrentPage(1);
                  }
                }}
                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                min="1"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Current Page
              </label>
              <input
                type="number"
                value={currentPage}
                onChange={(e) => {
                  const value = Math.max(
                    1,
                    Math.min(totalPages, parseInt(e.target.value) || 1),
                  );
                  setCurrentPage(value);
                }}
                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                min="1"
                max={totalPages}
              />
            </div>
          </div>

          <div className="rounded-lg bg-blue-50 p-4">
            <p className="text-sm font-medium text-blue-900">
              Pagination Info:
            </p>
            <div className="mt-2 grid grid-cols-2 gap-4 text-sm text-blue-800 sm:grid-cols-4">
              <p>
                <strong>Page:</strong> {currentPage} of {totalPages}
              </p>
              <p>
                <strong>Showing:</strong>{" "}
                {Math.min((currentPage - 1) * itemsPerPage + 1, totalItems)}-
                {Math.min(currentPage * itemsPerPage, totalItems)}
              </p>
              <p>
                <strong>Total Items:</strong> {totalItems}
              </p>
              <p>
                <strong>Per Page:</strong> {itemsPerPage}
              </p>
            </div>
          </div>

          <Pagination
            {...args}
            totalCount={totalItems}
            pageNumber={currentPage}
            rowsPerPage={itemsPerPage}
            setPageNumber={setCurrentPage}
          />
        </div>
      );
    };

    return <InteractivePagination />;
  },
};

// Large dataset pagination
export const LargeDataset: Story = {
  args: {
    totalCount: 10000,
    pageNumber: 500,
    rowsPerPage: 20,
    maxVisiblePages: 7,
    showFirstLast: true,
    setPageNumber: () => {},
  },
  render: (args) => {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Large Dataset</h3>
        <p className="text-sm text-gray-600">
          Pagination for large datasets (10,000 items, 500 pages, showing page
          500).
        </p>
        <div className="rounded-lg bg-gray-100 p-4">
          <p className="text-sm">
            <strong>Total Items:</strong> {args.totalCount.toLocaleString()}
          </p>
          <p className="text-sm">
            <strong>Current Page:</strong> {args.pageNumber}
          </p>
          <p className="text-sm">
            <strong>Items per Page:</strong> {args.rowsPerPage}
          </p>
          <p className="text-sm">
            <strong>Total Pages:</strong>{" "}
            {Math.ceil(args.totalCount / args.rowsPerPage).toLocaleString()}
          </p>
          <p className="text-sm">
            <strong>Showing Items:</strong>{" "}
            {((args.pageNumber - 1) * args.rowsPerPage + 1).toLocaleString()}-
            {Math.min(
              args.pageNumber * args.rowsPerPage,
              args.totalCount,
            ).toLocaleString()}
          </p>
        </div>
        <Pagination {...args} />
      </div>
    );
  },
};

// Edge cases
export const EdgeCases: Story = {
  render: () => {
    return (
      <div className="space-y-8">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">First Page</h3>
          <p className="text-sm text-gray-600">
            Pagination on the first page with disabled previous/first buttons.
          </p>
          <Pagination
            totalCount={100}
            pageNumber={1}
            rowsPerPage={10}
            setPageNumber={() => {}}
            maxVisiblePages={5}
            showFirstLast={true}
          />
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Last Page</h3>
          <p className="text-sm text-gray-600">
            Pagination on the last page with disabled next/last buttons.
          </p>
          <Pagination
            totalCount={100}
            pageNumber={10}
            rowsPerPage={10}
            setPageNumber={() => {}}
            maxVisiblePages={5}
            showFirstLast={true}
          />
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Small Dataset (Hidden)</h3>
          <p className="text-sm text-gray-600">
            When total pages ≤ 1, pagination is hidden (only 5 items with 10 per
            page).
          </p>
          <div className="rounded-lg bg-yellow-50 p-4">
            <p className="text-sm text-yellow-800">
              No pagination component shown - this is the expected behavior for
              small datasets.
            </p>
          </div>
          <Pagination
            totalCount={5}
            pageNumber={1}
            rowsPerPage={10}
            setPageNumber={() => {}}
            maxVisiblePages={5}
            showFirstLast={true}
          />
        </div>
      </div>
    );
  },
};

// Different configurations
export const Configurations: Story = {
  render: () => {
    return (
      <div className="space-y-8">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Compact (3 visible pages)</h3>
          <Pagination
            totalCount={1000}
            pageNumber={25}
            rowsPerPage={10}
            setPageNumber={() => {}}
            maxVisiblePages={3}
            showFirstLast={true}
          />
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Extended (7 visible pages)</h3>
          <Pagination
            totalCount={1000}
            pageNumber={25}
            rowsPerPage={10}
            setPageNumber={() => {}}
            maxVisiblePages={7}
            showFirstLast={true}
          />
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">No First/Last Buttons</h3>
          <Pagination
            totalCount={1000}
            pageNumber={25}
            rowsPerPage={10}
            setPageNumber={() => {}}
            maxVisiblePages={5}
            showFirstLast={false}
          />
        </div>
      </div>
    );
  },
};
