import { useState } from "react";

export function usePagination(initialPage = 1, initialRowsPerPage = 10) {
  const [pageNumber, setPageNumber] = useState(initialPage);
  const [rowsPerPage, setRowsPerPage] = useState(initialRowsPerPage);

  const resetToFirstPage = () => setPageNumber(1);

  return {
    pageNumber,
    setPageNumber,
    rowsPerPage,
    setRowsPerPage,
    resetToFirstPage,
  };
}
