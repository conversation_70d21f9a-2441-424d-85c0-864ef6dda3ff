import * as React from "react";

import DangerFillICon from "@workspace/ui/assets/icons/danger-fill.svg";
import { cn, developmentOnly } from "@workspace/ui/lib/utils";
import { cva, VariantProps } from "class-variance-authority";

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size">,
    VariantProps<typeof rootClassNameBinding> {
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
  rootClassName?: string;
  className?: string;
  helperText?: string;
  helperTextClassName?: string;
  fieldsetClass?: string;

  /**
   * @default "<Info />"
   * @description
   * Pass null if you want to hide default icon
   */
  helperTextIcon?: React.ReactNode | null;
}

const rootClassNameBinding = cva(
  "group relative flex w-full h-full items-center gap-1 bg-transparent",
  {
    variants: {
      variant: {
        outline: "",
        underline: "",
      },
      size: {
        sm: "ps-2 pe-2 h-8",
        md: "ps-3 pe-2 h-10",
        lg: "ps-3 pe-2 h-12",
      },
    },
    defaultVariants: {
      variant: "outline",
      size: "md",
    },
  },
);

const fieldsetClassName = cva(
  "pointer-events-none absolute top-0 right-0 bottom-0 left-0 peer-placeholder-shown:p-0 peer-placeholder-shown:[&_legend]:hidden peer-focus-within:[&_legend]:block peer-placeholder-shown:[&_legend]:max-w-0 peer-focus-within:[&_legend]:max-w-full aria-invalid:border-border-error-default peer-disabled:border-border-nautral-disable",
  {
    variants: {
      variant: {
        outline:
          "border border-border-nautral-default peer-focus-within:border-2 peer-hover:border-2 peer-focus-within:border-border-primary-default peer-hover:border-border-primary-default rounded-sm",
        underline:
          "border-b border-border-nautral-default peer-focus-within:border-border-primary-default peer-hover:border-border-primary-default peer-focus-within:border-b-2 peer-hover:border-b-2",
      },
    },
    defaultVariants: {
      variant: "outline",
    },
  },
);

const titleClassName = cva(
  "text-text-nautral-secondary peer-disabled:text-text-nautral-disable pointer-events-none absolute -top-[7px] text-[10px] leading-3 transition-all peer-placeholder-shown:top-1/2 peer-focus-within:-top-[7px] peer-placeholder-shown:-translate-y-1/2 peer-focus-within:translate-none",
  {
    variants: {
      size: {
        sm: "start-3 peer-placeholder-shown:start-2 peer-focus-within:start-3 peer-placeholder-shown:text-sm peer-focus-within:text-[10px]",
        md: "start-3 peer-placeholder-shown:start-3 peer-focus-within:start-3 peer-placeholder-shown:text-sm peer-focus-within:text-[10px]",
        lg: "start-3 peer-placeholder-shown:text-sm peer-focus-within:text-[10px]",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

const helperTextClasses = cva(
  "text-text-nautral-secondary aria-invalid:text-text-error-default-2 aria-disabled:text-text-nautral-disable flex items-center gap-[5px]",
  {
    variants: {
      size: {
        sm: "ps-2 pe-2 text-[8px]",
        md: "ps-3 pe-2 text-[10px]",
        lg: "ps-3 pe-2 text-[10px]",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      rootClassName,
      type,
      startAdornment,
      endAdornment,
      variant = "outline",
      size = "md",
      helperText,
      helperTextIcon,
      helperTextClassName,
      fieldsetClass,
      ...props
    },
    ref,
  ) => {
    return (
      <div className="h-full w-full">
        <div
          className={cn(
            props?.disabled && "cursor-not-allowed",
            rootClassNameBinding({ variant, size }),
            rootClassName,
          )}
          aria-invalid={props?.["aria-invalid"]}
        >
          {startAdornment && (
            <div className="text-muted-foreground flex items-center justify-center">
              {startAdornment}
            </div>
          )}

          <input
            ref={ref}
            type={type}
            data-slot="input"
            className={cn(
              "peer file:text-foreground text-text-nautral-default selection:bg-primary selection:text-primary-foreground h-full min-w-0 flex-1 bg-transparent text-sm outline-none file:inline-flex file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm dark:bg-transparent",
              !props?.title && !!props?.placeholder
                ? "placeholder:!text-text-nautral-secondary"
                : "focus-within:placeholder:!text-text-nautral-secondary placeholder:!text-transparent",
              className,
            )}
            {...props}
            placeholder={props?.placeholder || " "}
          />

          {endAdornment && (
            <div className="text-muted-foreground flex items-center justify-center">
              {endAdornment}
            </div>
          )}

          <fieldset
            className={cn(fieldsetClassName({ variant }) + "", fieldsetClass)}
            aria-invalid={props?.["aria-invalid"]}
          >
            {/* just white space placeholder below the field title */}
            {props?.title && (
              <legend className="ms-2 h-0 ps-1 pe-1 text-[10px] opacity-0">
                {props?.title}{" "}
                {props?.required || props?.["aria-required"] ? (
                  <span>*</span>
                ) : (
                  ""
                )}
              </legend>
            )}
          </fieldset>

          {/* field title + placeholder */}
          {props?.title && (
            <div
              className={cn(
                startAdornment &&
                  "peer-placeholder-shown:ms-5 peer-focus-within:ms-0",
                titleClassName({ size }),
              )}
              data-testid={developmentOnly("input-title")}
            >
              {props?.title}{" "}
              {props?.required || props?.["aria-required"] ? (
                <span className="text-text-error-default-2">*</span>
              ) : (
                ""
              )}
            </div>
          )}
        </div>

        {helperText && (
          <div
            className={cn(helperTextClasses({ size }), helperTextClassName)}
            aria-invalid={props?.["aria-invalid"]}
            aria-disabled={props?.disabled}
          >
            {helperTextIcon || <DangerFillICon className="size-3" />}
            {helperText}
          </div>
        )}
      </div>
    );
  },
);

Input.displayName = "Input";

export { Input };
