---
trigger:
  - demo-admin
pool:
  name: default
stages:
  - stage: Build
    displayName: Build demo-admin
    jobs:
      - job: Build
        steps:
          - script: docker login --username test --password test nexus.otcsaba.ir:8082
          - script: docker build -f ./apps/admin/Dockerfile -t adminfrontend:$(Build.BuildId) .
          - script: docker tag adminfrontend:$(Build.BuildId)
              nexus.otcsaba.ir:8082/investment/demoadmin/adminfrontend:$(Build.BuildId)
          - script: docker push nexus.otcsaba.ir:8082/investment/demoadmin/adminfrontend:$(Build.BuildId)
  - stage: Deploy
    displayName: Deploy demo-admin
    jobs:
      - job: Deploy
        steps:
          - task: SSH@0
            inputs:
              sshEndpoint: InvestmentDemo
              runOptions: commands
              commands: >
                docker pull nexus.otcsaba.ir:8082/investment/demoadmin/adminfrontend:$(Build.BuildId);
                docker service update --image nexus.otcsaba.ir:8082/investment/demoadmin/adminfrontend:$(Build.BuildId) adminfrontend --with-registry-auth
              readyTimeout: "20000"
            timeoutInMinutes: 20
