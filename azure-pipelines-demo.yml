---
trigger:
  - demo
pool:
  name: default
stages:
  - stage: Build
    displayName: Build demo
    jobs:
      - job: Build
        steps:
          - script: docker login --username test --password test nexus.otcsaba.ir:8082
          - script: docker build -f ./apps/web/Dockerfile -t frontend:$(Build.BuildId) .
          - script: docker tag frontend:$(Build.BuildId)
              nexus.otcsaba.ir:8082/investment/demo/frontend:$(Build.BuildId)
          - script: docker push nexus.otcsaba.ir:8082/investment/demo/frontend:$(Build.BuildId)
  - stage: Deploy
    displayName: Deploy demo
    jobs:
      - job: Deploy
        steps:
          - task: SSH@0
            inputs:
              sshEndpoint: InvestmentDemo
              runOptions: commands
              commands: >
                docker pull nexus.otcsaba.ir:8082/investment/demo/frontend:$(Build.BuildId);
                docker service update --image nexus.otcsaba.ir:8082/investment/demo/frontend:$(Build.BuildId) frontend --with-registry-auth
              readyTimeout: "20000"
            timeoutInMinutes: 20
