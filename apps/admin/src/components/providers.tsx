"use client";

import { initializeApiInterceptors } from "@/utils/initializeApiInterceptors";
import ReactQueryProvider from "@workspace/investment-api/reactQueryProvider";
import { Toaster } from "@workspace/ui/components/toast";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import * as React from "react";

export function Providers({ children }: { children: React.ReactNode }) {
  React.useEffect(() => {
    initializeApiInterceptors();
  }, []);

  return (
    <ReactQueryProvider>
      <NextThemesProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
        enableColorScheme
      >
        {children}
        <Toaster />
      </NextThemesProvider>
    </ReactQueryProvider>
  );
}
