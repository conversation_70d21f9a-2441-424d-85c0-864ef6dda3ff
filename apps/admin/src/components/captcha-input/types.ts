import { InputProps } from "@workspace/ui/components/input";

export const CaptchaTypeEnum = {
  None: 0,
  GoogleRecaptchaV3: 100,
  Text: 200,
} as const;

export type CaptchaTypeEnum =
  (typeof CaptchaTypeEnum)[keyof typeof CaptchaTypeEnum];

export interface ApiResultOfCaptchaTypeEnum {
  data?: CaptchaTypeEnum;
  isSuccess?: boolean;
  errorCode?: string | null;
  errorMessage?: string | null;
}

export interface RequestCaptchaOutputDTO {
  captchaImage?: string;
  captchaSecretKey?: string;
}

export type ApiResultOfRequestCaptchaOutputDTOData =
  RequestCaptchaOutputDTO | null;

export interface ApiResultOfRequestCaptchaOutputDTO {
  data?: ApiResultOfRequestCaptchaOutputDTOData;
  isSuccess?: boolean;
  errorCode?: string | null;
  errorMessage?: string | null;
}

// Hook types
export interface UseCaptchaWhichCaptchaIsActiveReturn {
  data?: ApiResultOfCaptchaTypeEnum;
  isLoading?: boolean;
  error?: any;
}

export interface UseCaptchaRequestTextCaptchaReturn {
  data?: ApiResultOfRequestCaptchaOutputDTO;
  isLoading?: boolean;
  error?: any;
  refetch?: () => void;
}

export interface UseCaptchaRequestTextCaptchaOptions {
  query?: {
    enabled?: boolean;
    refetchInterval?: number;
  };
}

export interface CaptchaInputProps {
  useCaptchaWhichCaptchaIsActive: () => UseCaptchaWhichCaptchaIsActiveReturn;
  useCaptchaRequestTextCaptcha: (
    options?: UseCaptchaRequestTextCaptchaOptions
  ) => UseCaptchaRequestTextCaptchaReturn;
  onCaptchaChange: (captchaData: {
    secretKey?: string;
    captchaCode?: string;
  }) => void;
  googleRecaptchaSiteKey: string;
  className?: string;
  error?: string;
  inputProps?: InputProps;
  setErrorMessage?: (value: string) => void;
  setIsLoading: (value: boolean) => void;
  setCaptchaType?: (value: CaptchaTypeEnum) => void;
}
