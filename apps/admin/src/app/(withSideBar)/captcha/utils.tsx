import {
  CaptchaSettingsHistoryResponse,
  CaptchaTypeEnum,
} from "@workspace/investment-api/auto-generated/models";
import { dateConverter } from "@workspace/ui/lib/dateConverter";
import { ColDef } from "ag-grid-community";
import { Loading } from "@workspace/ui/components/loading";

interface ICaptcha {
  data: IData;
}

interface IData {
  actionDate?: string;
  captchaType?: CaptchaTypeEnum;
}

export function defaultColDefs(): ColDef<CaptchaSettingsHistoryResponse>[] {
  return [
    {
      field: "actionDate",
      headerName: "تاربخ و زمان",
      width: 200,
      flex: 1,
      sortable: false,
      cellRenderer: (data: ICaptcha) => {
        return (
          <div>
            {data?.data?.actionDate &&
              dateConverter(data?.data?.actionDate).format(
                "YYYY/MM/DD - HH:MM:ss"
              )}
          </div>
        );
      },
    },
    {
      field: "captchaType",
      headerName: "نوع کپچا",
      width: 200,
      flex: 1,
      sortable: false,
      cellRenderer: (data: ICaptcha) => {
        return (
          <div>
            {data?.data?.captchaType === CaptchaTypeEnum?.GoogleRecaptchaV3
              ? "بین المللی"
              : "داخلی"}
          </div>
        );
      },
    },
  ];
}

export function CustomLoadingOverlay() {
  return (
    <div className="flex h-full items-center justify-center">
      <div className="flex min-w-28 items-center justify-center">
        <Loading className="h-6 w-6 animate-spin p-0.5" />
      </div>
    </div>
  );
}
