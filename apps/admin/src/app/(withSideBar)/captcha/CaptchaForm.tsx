import { useCaptchaHook } from "@/app/(withSideBar)/captcha/useCaptchaHook";
import { CaptchaTypeEnum } from "@/components/captcha-input/types";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/tabs";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { toast } from "@workspace/ui/components/toast";
import React from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useQueryClient } from "@tanstack/react-query";

function CaptchaForm() {
  const queryClient = useQueryClient();
  const { data, refetch, editSystemSettingAsync } = useCaptchaHook();

  const formSchema = z.object({
    captchaTypeEnum: z.number(),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      captchaTypeEnum: data?.data?.captchaType,
    },
    mode: "onSubmit",
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    const payload = {
      captchaTypeEnum:
        values.captchaTypeEnum === 100
          ? CaptchaTypeEnum.GoogleRecaptchaV3
          : CaptchaTypeEnum.Text,
    };
    return editSystemSettingAsync(
      { data: payload },
      {
        onSuccess: () => {
          toast.success("تغییرات با موفقیت ثبت شد", {
            position: "bottom-right",
          });

          queryClient.invalidateQueries({
            queryKey: [
              "/api/backoffice/v1/Settings/GetCaptchaSettingsHistoryPaging",
            ],
            exact: false,
          });

          // Refetch updated data
          refetch().then((refetchedData) => {
            // Reset form with fresh data to update defaultValues and isDirty state
            form.reset({
              captchaTypeEnum:
                refetchedData?.data?.data?.captchaType ??
                values.captchaTypeEnum,
            });
          });
        },
        onError: (error) => {
          toast.error("خطا در ثبت تغییرات", {
            description: error?.message || "خطایی رخ داده است",
          });
        },
      }
    );
  }

  return (
    <div className="flex w-full flex-col shrink-0">
      <div className="flex justify-between items-start">
        <div>
          <div className="font-bold text-3xl leading-14">کپچا(Captcha)</div>
          <div>برای ورود، ثبت نام، احراز هویت و فراموشی رمز عبور.</div>
        </div>

        <Button
          type="submit"
          form="login-form"
          disabled={!form.formState.isDirty || form.formState.isSubmitting}
        >
          ثبت تغییرات
        </Button>
      </div>

      <Form {...form}>
        <form
          id="login-form"
          onSubmit={form.handleSubmit(onSubmit)}
          className=" pt-3 "
        >
          <div className="flex items-center  gap-4 justify-between">
            <div>تاریخچه</div>
            <div className="flex flex-col gap-6 justify-between">
              <FormField
                control={form.control}
                name="captchaTypeEnum"
                render={({ field, fieldState }) => {
                  return (
                    <FormItem>
                      <FormControl>
                        <Tabs
                          {...field}
                          value={`${field.value}`}
                          onValueChange={(value) => {
                            field.onChange(Number(value));
                          }}
                        >
                          <TabsList>
                            <TabsTrigger value="100">بین المللی</TabsTrigger>
                            <TabsTrigger value="200">داخلی</TabsTrigger>
                          </TabsList>
                        </Tabs>
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}

export default CaptchaForm;
