import {
  useSettingsEditSystemSettings,
  useSettingsGetSystemSettings,
} from "@workspace/investment-api/auto-generated/apis/settings/settings";

export const useCaptchaHook = () => {
  const { data, isLoading, refetch } = useSettingsGetSystemSettings();

  const { mutateAsync: editSystemSettingAsync } =
    useSettingsEditSystemSettings();

  return { data, isLoading, refetch, editSystemSettingAsync };
};
