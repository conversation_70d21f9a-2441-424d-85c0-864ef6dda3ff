import CaptchaForm from "@/app/(withSideBar)/captcha/CaptchaForm";
import CaptchaHistoryTable from "@/app/(withSideBar)/captcha/CaptchaHistoryTable";

import { useCaptchaHook } from "@/app/(withSideBar)/captcha/useCaptchaHook";
import { Loading } from "@workspace/ui/components/loading";
import React from "react";

function Captcha() {
  const { isLoading } = useCaptchaHook();

  return (
    <div className="p-6 h-screen">
      {isLoading ? (
        <div className="flex items-center justify-center min-h-svh">
          <Loading />
        </div>
      ) : (
        <div className="flex flex-col h-full">
          <CaptchaForm />
          <CaptchaHistoryTable />
        </div>
      )}
    </div>
  );
}

export default Captcha;
