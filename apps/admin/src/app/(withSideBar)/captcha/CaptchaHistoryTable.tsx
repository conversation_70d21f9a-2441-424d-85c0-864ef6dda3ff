import {
  CustomLoadingOverlay,
  defaultColDefs,
} from "@/app/(withSideBar)/captcha/utils";
import { useSettingsGetCaptchaSettingsHistoryPaging } from "@workspace/investment-api/auto-generated/apis/settings/settings";
import { CaptchaSettingsHistoryResponse } from "@workspace/investment-api/auto-generated/models";
import Table from "@workspace/ui/components/ag-grid/AgGrid";
import { Pagination } from "@workspace/ui/components/pagination";
import { usePagination } from "@workspace/ui/components/pagination/usePaginationHook";
import React, { useMemo } from "react";

function CaptchaHistoryTable() {
  const { pageNumber, setPageNumber, rowsPerPage } = usePagination(1, 10);

  const handlePageChange = (newPage: number) => {
    setPageNumber(newPage);
  };

  const params = {
    PageNumber: pageNumber,
    PageSize: 10,
  };

  const { data: captchaSettingHistoryPaging } =
    useSettingsGetCaptchaSettingsHistoryPaging(params);

  const totalCount = captchaSettingHistoryPaging?.data?.totalCount;

  const columnDefsData = useMemo(() => defaultColDefs(), []);

  return (
    <div className="h-full w-full flex flex-col">
      <div className="w-full mt-10 mb-20 flex-1 shrink-0 min-h-0" dir="rtl">
        <Table<CaptchaSettingsHistoryResponse>
          columnDefs={columnDefsData}
          rowData={captchaSettingHistoryPaging?.data?.items}
          enableRtl={true}
          loadingOverlayComponent={CustomLoadingOverlay}
        />
      </div>

      {!!totalCount && (
        <div className="flex shrink-0 pb-10">
          <Pagination
            totalCount={totalCount}
            pageNumber={pageNumber}
            rowsPerPage={rowsPerPage}
            setPageNumber={handlePageChange}
          />
        </div>
      )}
    </div>
  );
}

export default CaptchaHistoryTable;
