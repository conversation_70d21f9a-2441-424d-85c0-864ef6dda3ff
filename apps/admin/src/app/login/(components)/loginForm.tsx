import { useLoginHook } from "@/app/login/useLoginHook";
import <PERSON><PERSON><PERSON><PERSON> from "@/assets/icons/saba-login-logo.svg";
import { CaptchaInput } from "@/components/captcha-input";
import { CaptchaInputRef } from "@/components/captcha-input/captcha-input";
import { CaptchaTypeEnum } from "@/components/captcha-input/types";
import { SplashLoading } from "@/components/splash-loading";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCaptchaRequestTextCaptcha2,
  useCaptchaWhichCaptchaIsActive2,
} from "@workspace/investment-api/auto-generated/apis/captcha/captcha";
import { Button } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { InputPassword } from "@workspace/ui/components/input-password";
import { Loading } from "@workspace/ui/components/loading";
import { useRef, useState } from "react";
import { useForm, UseFormSetError } from "react-hook-form";
import { z } from "zod";

const formSchema = z.object({
  password: z.string().nonempty("رمز عبور را وارد کنید."),
  username: z.string().nonempty("نام کاربری را وارد کنید."),
  secretKey: z.string().nonempty("این فیلد اجباری است"),
  captchaCode: z.string(),
});
export type LoginFormValues = z.infer<typeof formSchema>;

export type SetErrorLogin = UseFormSetError<z.infer<typeof formSchema>>;

export type LoginFormHandleSubmitFunction = (
  values: LoginFormValues,
  { setError }: { setError: SetErrorLogin }
) => Promise<unknown>;

export interface LoginFormProps {
  handleSubmit: LoginFormHandleSubmitFunction;
  captchaType: CaptchaTypeEnum | null;
  setCaptchaType: (value: CaptchaTypeEnum | null) => void;
}

function LoginForm() {
  const { handleLogin, captchaType, setCaptchaType } = useLoginHook();

  const captchaInputRef = useRef<CaptchaInputRef>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
      username: "",
      secretKey: "",
      captchaCode: "",
    },
    mode: "onSubmit",
  });

  const secretKey = form.watch("secretKey");

  const [isLoading, setIsLoading] = useState(
    captchaType === CaptchaTypeEnum.GoogleRecaptchaV3 ? !secretKey : true
  );

  function onSubmit(values: z.infer<typeof formSchema>) {
    captchaInputRef.current?.refreshCaptcha();
    return handleLogin(values);
  }

  return (
    <>
      <div className="flex flex-col justify-between gap-6">
        <SabaLogo className="text-icon-neutral-default mx-auto h-[76px] w-[288px]" />

        <div className="font-bold">ورود به داشبورد مدیریتی</div>

        <div className="text-sm font-light">
          بــــرای ورود به حساب کاربری خود لـــطفا نام کاربری و رمز عبور خود را
          وارد کنید.
        </div>
        <Form {...form}>
          <form
            id="login-form"
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col justify-between pt-3 "
          >
            {isLoading && <SplashLoading />}
            <div className="flex flex-col gap-4 justify-between">
              <div className="flex flex-col gap-6 justify-between">
                <FormField
                  control={form.control}
                  name="username"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          size="lg"
                          title="نام کاربری"
                          {...field}
                          helperText={fieldState?.error?.message}
                          aria-invalid={!!fieldState?.error?.message}
                          name="username"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormControl>
                        <InputPassword
                          size="lg"
                          title="رمز عبور"
                          {...field}
                          helperText={fieldState?.error?.message}
                          aria-invalid={!!fieldState?.error?.message}
                          name="password"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="secretKey"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <CaptchaInput
                        useCaptchaWhichCaptchaIsActive={
                          useCaptchaWhichCaptchaIsActive2
                        }
                        useCaptchaRequestTextCaptcha={
                          useCaptchaRequestTextCaptcha2
                        }
                        onCaptchaChange={({ secretKey, captchaCode }) => {
                          field.onChange(secretKey);
                          form.setValue("captchaCode", captchaCode || "");
                        }}
                        googleRecaptchaSiteKey={
                          process.env.NEXT_PUBLIC_RECAPTCHA || ""
                        }
                        inputProps={{
                          title: "کد  مقابل را وارد کنید",
                          placeholder: " ",
                          helperText: fieldState?.error?.message,
                          "aria-invalid": !!fieldState?.error?.message,
                        }}
                        setIsLoading={setIsLoading}
                        ref={captchaInputRef}
                        setCaptchaType={setCaptchaType}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
      </div>

      <Button
        className="w-full mt-4"
        variant="fill"
        size="lg"
        type="submit"
        form="login-form"
        disabled={form.formState.isSubmitting}
        endAdornment={form.formState.isSubmitting && <Loading size="sm" />}
        data-test="c635cdf0-41e2-4778-a78a-537fe514c8b5"
      >
        ورود
      </Button>
    </>
  );
}
export default LoginForm;
