import { LoginFormValues } from "@/app/login/(components)/loginForm";
import { CaptchaTypeEnum } from "@/components/captcha-input/types";
import { COOKIE_IS_LOGGED_IN } from "@/constants/cookies";
import { ROUTE_HOME } from "@/constants/routes";
import { setCookie } from "@/hooks/useCookie";
import { useAuthLogin } from "@workspace/investment-api/auto-generated/apis/auth/auth";
import { toast } from "@workspace/ui/components/toast";
import { useRouter } from "next/navigation";
import { useState } from "react";

export const useLoginHook = () => {
  const router = useRouter();

  const [captchaType, setCaptchaType] = useState<CaptchaTypeEnum | null>(null);

  const [captchaKey, setCaptchaKey] = useState("");
  const [captchaCode, setCaptchaCode] = useState("");


  const loginMethodHeaders =
    captchaType === CaptchaTypeEnum.GoogleRecaptchaV3
      ? {
          "captcha-code": captchaKey,
          "Content-Type": "multipart/form-data",
        }
      : {
          "captcha-code": captchaCode,
          "captcha-secret-key": captchaKey,
          "Content-Type": "multipart/form-data",
        };

  const { mutateAsync: authLogin } = useAuthLogin({
    request: {
      headers: loginMethodHeaders,
    },
  });

  const handleLogin = async (value: LoginFormValues) => {
   
    setCaptchaKey(value?.secretKey);
    setCaptchaCode(value?.captchaCode);

    return authLogin({
      data: { password: value?.password, username: value?.username },
    })
      .then(() => {
        router.replace(ROUTE_HOME);
        setCookie(COOKIE_IS_LOGGED_IN, "true");
      })
      .catch((err) => {
        toast.error(err.response?.data?.errorMessage, {
          position: "bottom-right",
        });
      });
  };
  return { handleLogin, captchaType, setCaptchaType };
};
