import type { Preview } from "@storybook/react";

// Import our dedicated Storybook Tailwind CSS v4 configuration
import "./tailwind.css";

// Import Storybook-specific styles
import "../font.css";
import "./storybook.css";

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    // controls: {
    //   matchers: {
    //     color: /(background|color)$/i,
    //     date: /Date$/i,
    //   },
    // },
    // backgrounds: {
    //   default: "light",
    //   values: [
    //     { name: "light", value: "#28282C" },
    //     { name: "dark", value: "#28282C" },
    //   ],
    // },
  },
  globalTypes: {
    theme: {
      description: "Global theme for components",
      defaultValue: "light",
      toolbar: {
        title: "Theme",
        icon: "circlehollow",
        items: ["light", "dark"],
        dynamicTitle: true,
      },
    },
  },
  decorators: [
    (Story, context) => {
      const theme = context.globals.theme;
      document.body.className = theme === "dark" ? "dark" : "";
      return Story();
    },
  ],
};

export default preview;
