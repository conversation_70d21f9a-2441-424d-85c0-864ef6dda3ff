FROM nexus.otcsaba.ir:8083/node:20-alpine AS base

RUN apk update
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat

# Configure npm registry globally for all stages
RUN npm config set registry https://nexus.otcsaba.ir/repository/npm/ && \
    npm config set strict-ssl false && \
    npm config get registry

# Install pnpm globally using npm (more reliable than corepack)
RUN npm install -g pnpm@10.4.1 turbo http-server

# ---------------------------------------------------------------------------- #
#                               builder stage                                  #
# ---------------------------------------------------------------------------- #
FROM base AS builder
WORKDIR /app

# First copy only package.json and pnpm-lock.yaml to leverage Docker cache
COPY package.json pnpm-workspace.yaml turbo.json ./
COPY pnpm-lock.yaml ./
COPY apps/storybook/package.json ./apps/storybook/
COPY apps/web/package.json ./apps/web/
COPY packages/eslint-config/package.json ./packages/eslint-config/
COPY packages/typescript-config/package.json ./packages/typescript-config/
COPY packages/ui/package.json ./packages/ui/
COPY packages/investment-api/package.json ./packages/investment-api/

# Install dependencies
RUN pnpm install

# Copy only the necessary files for the build
COPY apps/storybook ./apps/storybook
COPY apps/web/src ./apps/web/src
COPY packages ./packages

# Build storybook
RUN pnpm turbo build --filter=storybook

# ---------------------------------------------------------------------------- #
#                              installer stage                                 #
# ---------------------------------------------------------------------------- #
# Add lockfile and package.json's of isolated subworkspace
FROM base AS installer
WORKDIR /app

# Copy the package.json files
COPY package.json pnpm-workspace.yaml turbo.json ./
COPY pnpm-lock.yaml ./
COPY apps/storybook/package.json ./apps/storybook/
COPY apps/web/package.json ./apps/web/
COPY packages/eslint-config/package.json ./packages/eslint-config/
COPY packages/typescript-config/package.json ./packages/typescript-config/
COPY packages/ui/package.json ./packages/ui/
COPY packages/investment-api/package.json ./packages/investment-api/

# Install dependencies
RUN pnpm install

# Copy the source files
COPY apps/storybook ./apps/storybook
COPY apps/web/src ./apps/web/src
COPY packages ./packages

# Build storybook
RUN pnpm turbo build --filter=storybook

# ---------------------------------------------------------------------------- #
#                                 runner stage                                 #
# ---------------------------------------------------------------------------- #
# Production stage - serve static files with http-server
FROM base AS runner
WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 storybook

# Copy only the built storybook static files
COPY --from=installer /app/apps/storybook/storybook-static ./storybook-static

# Change ownership of the app directory to the storybook user
RUN chown -R storybook:nodejs /app
USER storybook

# Expose the port where you want to serve Storybook
EXPOSE 3000

# Serve the generated storybook-static folder using http-server
CMD ["npx", "http-server", "./storybook-static", "-p", "3000"]

