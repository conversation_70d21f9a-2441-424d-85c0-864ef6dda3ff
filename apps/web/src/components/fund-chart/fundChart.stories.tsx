import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import FundChart from "@/components/fund-chart";
import { LinearGradientColorObject } from "highcharts";

const meta: Meta<typeof FundChart> = {
  title: "Web/fundChart",
  component: FundChart,
  parameters: {
    layout: "centered",
  },
  // tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="p-8">
        <div
          style={{ height: 230, padding: 36 }}
          className="bg-surface-nautral-transparent container max-w-sm"
          dir="rtl"
        >
          <Story />
        </div>
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof FundChart>;

export const Chart: Story = {
  args: {
    series: [
      {
        type: "areaspline",
        name: "سهام",
        data: [59, 83, 65, 228, 184],
        color: "#5E8CE1",
        legendIndex: 1,
        fillColor: {
          linearGradient: { x1: 0, x2: 0, y1: 0, y2: 300 },
          stops: [
            [0, "rgba(103, 154, 247, 0.4)"],
            [1, "rgba(103, 154, 247, 0.1)"],
          ],
        },
        marker: {
          enabled: false,
        },
      },
      {
        type: "spline",
        name: "صندوق",
        color: "#FF928A",
        legendIndex: 0,
        data: [5, 20.33, 77.66, 11.33, 70.66],
      },
    ],
  },
};
