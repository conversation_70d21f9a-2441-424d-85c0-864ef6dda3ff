import { Options } from "highcharts";
import { dateConverter } from "@/utils/dateConverter";
import { toFixed } from "@workspace/ui/lib/utils";

const chartOption: Options = {
  xAxis: { visible: false },
  yAxis: {
    title: {
      text: "",
    },
    gridLineColor: "var(--border-nautral-disable)",
    gridLineWidth: 1,
    labels: {
      enabled: false,
    },
  },
  chart: {
    backgroundColor: "transparent",
    plotBackgroundColor: "transparent",
    plotBorderWidth: 0,
    plotShadow: false,
    animation: false,
    margin: 0,
    spacingTop: 0,
    spacingBottom: 0,
    spacingLeft: 0,
    spacingRight: 0,
  },
  tooltip: {
    /* @ts-ignore */
    useHTML: true,
    backgroundColor: "transparent",
    borderWidth: 0,
    shadow: false,
    /* @ts-ignore */
    // eslint-disable-next-line
    formatter: function () {
      let date1 = "";

      /* @ts-ignore */
      this.points?.forEach((i) => {
        const d = dateConverter(new Date(i.x).toString());
        date1 = d.format("YYYY/M/D");
      });

      let s = `
              <div style="font-family: var(--font-yekan),serif !important" class="min-w-[120px] min-h-[60px] bg-surface-nautral-default-1 p-2 pt-1 rounded-lg text-text-nautral-secondary text-[8px] font-yekan flex flex-col justify-between gap-1">
                  <div class="text-center m-auto pb-1">${date1}</div>
              `;

      /* @ts-ignore */
      this.points?.forEach((i) => {
        const { y: value, series, color } = i;
        s += `
                  <div class="flex justify-between">
                    <div>${toFixed(value)}٪</div>
                  
                    <div class="flex">
<span>${series.name}</span>
                    
                    <div style="background-color: ${color}" class="w-2 h-2 rounded-[2px] ml-1"></div>
                    </div>
                  </div>
              `;
      });

      s += "</div>";

      return s;
    },
    shared: true,
  },
  plotOptions: {
    areaspline: {
      marker: {
        enabled: false,
        states: {
          hover: {
            enabled: true,
          },
        },
      },
    },
    spline: {
      marker: {
        enabled: false,
        states: {
          hover: {
            enabled: true,
          },
        },
      },
    },
  },
  title: { text: "" },
  subtitle: { text: "" },
  credits: { enabled: false },
  legend: {
    align: "right",
    verticalAlign: "top",
    x: 6,
    itemDistance: 12,
    floating: false,
    borderWidth: 0,
    backgroundColor: "transparent",
    symbolHeight: 0,
    symbolWidth: 0,
    useHTML: true,
    events: {
      itemClick: function () {
        return false;
      },
    },
    // @ts-ignore
    labelFormatter: function () {
      // @ts-ignore
      return `<div style="color: var(--text-nautral-default)" class="flex"><span class="text-[10px] relative" style="bottom: 3px;left: 4px ;font-family: var(--font-yekan),serif !important">${this.name}</span><div style="background-color: ${this?.visible ? this.color : "var(--border-nautral-disable)"}; border-radius: 4px; margin-left: 7px" class="w-3 h-3 inline-block"></div></div>`;
    },
  },
};

export default chartOption;
