import React from "react";
import ArrowUp from "@/assets/icons/arrow-up.svg";

const AssetTable = () => {
  return (
    <div className="[&_.name]:text-text-nautral-secondary [&_.value]:text-text-nautral-default p-[3px] pt-2 [&_.name]:text-[10px] [&_.value]:text-end [&_.value]:text-xs">
      <div className="border-b-surface-nautral-modal-cover grid grid-cols-3 border-b pt-2 pb-1.5">
        <div className="name">درحال بررسی</div>
        <div className="value">
          1
          <ArrowUp className="text-text-success-default mr-1.5 ml-2.5 inline size-4" />
        </div>
        <div className="value">
          5
          <ArrowUp className="text-icon-error-hover mr-1.5 inline size-4 rotate-180" />
        </div>
      </div>
      <div className="border-b-surface-nautral-modal-cover grid grid-cols-3 border-b pt-1 pb-1.5">
        <div className="name">تایید شده</div>
        <div className="value">
          0
          <ArrowUp className="text-text-success-default mr-1.5 ml-2.5 inline size-4" />
        </div>
        <div className="value">
          0
          <ArrowUp className="text-icon-error-hover mr-1.5 inline size-4 rotate-180" />
        </div>
      </div>
      <div className="grid grid-cols-3 pt-1.5 pb-1">
        <div className="name">لغو شده</div>
        <div className="value">
          0
          <ArrowUp className="text-text-success-default mr-1.5 ml-2.5 inline size-4" />
        </div>
        <div className="value">
          0
          <ArrowUp className="text-icon-error-hover mr-1.5 inline size-4 rotate-180" />
        </div>
      </div>
    </div>
  );
};

export default AssetTable;
