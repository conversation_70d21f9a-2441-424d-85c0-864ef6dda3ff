import { dateConverter } from "@workspace/ui/lib/dateConverter";
import Refresh2 from "@/assets/icons/refresh-2.svg";
import Pie<PERSON><PERSON> from "@/components/pie-chart";
import useUserConfig from "@/hooks/useUserConfig";
import EyeLine from "@/assets/icons/eye-line.svg";
import Eye from "@/assets/icons/eye.svg";
import commaSeparator from "@workspace/ui/lib/commaSeparator";
import { Loader } from "lucide-react";
import AssetTable from "@/components/userAsset/componenets/asset-table";
import { IUserAsset } from "./types";
import DocumentText from "@/assets/icons/copy.svg";

// todo remove and replace this with real data
export const userAssetMockData = {
  chartData: [5, 20.33, 77.66, 11.33, 70.66],
  activeFund: 3,
  todayChange: 3.2,
  updateDate: new Date(2025, 4, 15, 21, 22, 55),
  amount: 555555555555,
};

const UserAsset = ({
  onRefresh,
  data = userAssetMockData,
  loading,
}: IUserAsset) => {
  const { configs, setConfig } = useUserConfig();

  const showSensitiveData = configs.showSensitiveData ?? false;

  return (
    <div className="p-4 pb-0">
      <div className="bg-surface-nautral-default-4 rounded-lg">
        <div className="flex justify-between pl-2">
          <div className="flex shrink-0 flex-col justify-between gap-4 pt-3 pr-4 pb-3">
            <div className="text-text-nautral-default text-xs font-bold">
              مجموع دارایی شما
            </div>
            {!!data?.amount && (
              <div>
                <span className="text-text-nautral-default pl-1 text-sm">
                  {showSensitiveData ? data?.activeFund : "******"}
                </span>
                <span className="text-text-nautral-secondary text-[10px]">
                  {" "}
                  صندوق فعال
                </span>
              </div>
            )}
            <div className="flex pt-2">
              <Refresh2
                onClick={onRefresh}
                className="relative top-0.5 ml-1 h-2 w-2"
              />

              <span className="text-[8px]">
                {dateConverter(data?.updateDate)
                  .calendar("persian")
                  .locale("fa")
                  .format("ddd D MMM YYYY - hh:mm:ss")}
              </span>
            </div>
          </div>

          {!!data?.amount && <PieChart data={data?.chartData} />}
          {!data?.amount && (
            <div className="bg-zebra border-surface-nautral-default-3 mt-2.5 mb-[14px] ml-2 h-24 w-24 rounded-full border" />
          )}
        </div>
        <div className="pt-1 pr-2 pb-2 pl-2">
          <div className="bg-zebra mb-2 flex items-center justify-between rounded-sm px-2 py-1 pr-1">
            <div className="flex items-center justify-between gap-3">
              <div
                className="bg-surface-nautral-default-3 rounded-sm p-1"
                onClick={() =>
                  setConfig("showSensitiveData", !showSensitiveData)
                }
              >
                {showSensitiveData ? (
                  <EyeLine className="size-4 cursor-pointer rounded-sm" />
                ) : (
                  <Eye className="bg-surface-nautral-default-1 text-icon-neutral-default size-4 cursor-pointer rounded-sm" />
                )}
              </div>
              {loading && <Loader className="h-4 w-4 animate-spin" />}
              {!loading && (
                <div className="text-text-nautral-default text-xs">
                  {showSensitiveData
                    ? commaSeparator(`${data?.amount || "صفر"}`)
                    : "**********"}
                </div>
              )}
            </div>
            <div className="text-text-nautral-secondary text-[10px]">ریال</div>
          </div>
          <div className="bg-zebra flex flex-col rounded-sm pt-1 pr-1 pb-0.5 pb-1 pl-0.5">
            <div className="flex justify-between pl-1.5">
              <div className="text-text-nautral-secondary pr-1 text-[10px] font-bold">
                وضعیت درخواست‌ها
              </div>
              <DocumentText className="text-icon-primary-default bg-button-primary-default-2 size-6 rounded-sm p-1" />
            </div>
            <AssetTable />
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserAsset;
