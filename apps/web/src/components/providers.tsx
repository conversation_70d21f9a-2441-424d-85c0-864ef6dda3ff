"use client";

import * as React from "react";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import { Toaster } from "@workspace/ui/components/toast";
import ReactQueryProvider from "@workspace/investment-api/reactQueryProvider";
import { initializeApiInterceptors } from "@/utils/initializeApiInterceptors";
import { ResponsiveModalProvider } from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import { DrawerProvider } from "@workspace/ui/components/drawer/use-drawer";
import { DialogProvider } from "@workspace/ui/components/dialog/use-dialog";
import ThemeWithUserConfig from "./ThemeWithUserConfig";

export function Providers({ children }: { children: React.ReactNode }) {
  React.useEffect(() => {
    initializeApiInterceptors();
  }, []);

  return (
    <NuqsAdapter>
      <ReactQueryProvider>
        <ThemeWithUserConfig>
          <DialogProvider>
            <DrawerProvider>
              <ResponsiveModalProvider>{children}</ResponsiveModalProvider>
            </DrawerProvider>
          </DialogProvider>
          <Toaster />
        </ThemeWithUserConfig>
      </ReactQueryProvider>
    </NuqsAdapter>
  );
}
