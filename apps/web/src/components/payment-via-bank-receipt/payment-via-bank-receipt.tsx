"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, DrawerTitle } from "@workspace/ui/components/drawer";
import CloseIcon from "@workspace/ui/assets/icons/close.svg";
import { Form, FormField } from "@workspace/ui/components/form";
import { FormControl, FormItem } from "@workspace/ui/components/form";
import { InputNumber } from "@workspace/ui/components/input-number";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import Calendar from "@workspace/ui/assets/icons/calendar.svg";
import { Input } from "@workspace/ui/components/input";
import ArrowDown from "@workspace/ui/assets/icons/arrow-down.svg";
import Textarea from "@workspace/ui/components/textarea/textarea";
import { Button } from "@workspace/ui/components/button";
import { Loading } from "@workspace/ui/components/loading";
import { accounts, successInfo } from "./utils";

const PaymentViaBankReceipt = ({
  onClose,
  openSelectModal,
  closeSelectModal,
  openDatePickerModal,
  onSuccessSubmit,
}: {
  onClose: () => void;
  openSelectModal: (
    accounts: {
      name: string;
      accountNumber: string;
    }[],
    onDateChange: (id: string) => void,
  ) => void;
  closeSelectModal: () => void;
  openDatePickerModal: (onDateChange: (date: Date) => void) => void;
  onSuccessSubmit: (info: { name: string; value: string }[]) => void;
}) => {
  const formSchema = z.object({
    price: z.string().nonempty("مبلغ نمیتواند خالی باشد."),
    account: z.string().nonempty("حساب نمیتواند خالی باشد."),
    receiptNumber: z.string().nonempty("شماره ‌رسید نمیتواند خالی باشد."),
    date: z.string().nonempty("تاریخ نمیتواند خالی باشد."),
    description: z.string(),
    receiptImage: z.string(),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      price: "",
      account: "",
      receiptNumber: "",
      date: "",
      description: "",
      receiptImage: "",
    },
    // mode: "onBlur",
  });

  function onSubmit() {
    onSuccessSubmit(successInfo);
  }

  function onBankSelect(id: string) {
    form?.setValue("account", id);
    closeSelectModal();
  }

  function handleDateChange(date: Date) {
    form.setValue("date", date?.toString());
  }

  return (
    <div className="flex h-full flex-col">
      <DrawerHeader>
        <DrawerTitle className="border-b-surface-nautral-modal-cover text-text-nautral-default flex items-center justify-between border-b pb-3 text-xs">
          <div>پرداخت از طریق فیش بانکی</div>
          <div className="bg-surface-nautral-transparent rounded p-1.5">
            <CloseIcon
              onClick={onClose}
              className="text-icon-on-surface-primary size-2.5 cursor-pointer"
            />
          </div>
        </DrawerTitle>
      </DrawerHeader>
      <div className="flex grow flex-col justify-between px-4">
        <Form {...form}>
          <form
            id="recipt-buy-form"
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <FormField
              control={form.control}
              name="account"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      size="lg"
                      title="شماره حساب بانکی"
                      onClick={() => openSelectModal(accounts, onBankSelect)}
                      readOnly
                      {...field}
                      name="account"
                      endAdornment={<ArrowDown className="size-4" />}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="price"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormControl>
                    <InputNumber
                      size="lg"
                      commaSeparated
                      title="مبلغ"
                      {...field}
                      autoFocus
                      helperText={fieldState?.error?.message}
                      aria-invalid={!!fieldState?.error?.message}
                      endAdornment={"ریال"}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="receiptNumber"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormControl>
                    <InputNumber
                      size="lg"
                      commaSeparated
                      title="شماره‌رسید"
                      {...field}
                      helperText={fieldState?.error?.message}
                      aria-invalid={!!fieldState?.error?.message}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="date"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      size="lg"
                      onClick={() => openDatePickerModal(handleDateChange)}
                      title="تاریخ رسید"
                      readOnly
                      {...field}
                      name="date"
                      endAdornment={
                        <Calendar className="text-text-nautral-default size-[18px]" />
                      }
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea
                      placeholder="توضیحات (اختیاری)"
                      className="min-h-20"
                      {...field}
                      name="description"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="receiptImage"
              render={() => (
                <FormItem>
                  <FormControl>
                    <div className="border-border-primary-default rounded-xl border border-dashed p-4">
                      تصویر فیش واریزی
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />
          </form>
        </Form>
        <Button
          variant="fill"
          size="md"
          type="submit"
          className="w-full"
          form="recipt-buy-form"
          disabled={form.formState.isSubmitting || !form.formState.isDirty}
          endAdornment={form.formState.isSubmitting && <Loading size="sm" />}
        >
          ارسال فیش واریزی
        </Button>
      </div>
    </div>
  );
};

export default PaymentViaBankReceipt;
