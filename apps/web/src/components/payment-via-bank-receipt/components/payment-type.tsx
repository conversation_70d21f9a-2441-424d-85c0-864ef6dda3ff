import { Button } from "@workspace/ui/components/button";
import {
  RadioGroup,
  RadioGroupItem,
} from "@workspace/ui/components/radio-group";
import { cn } from "@workspace/ui/lib/utils";
import React, { useState } from "react";

type PaymentType = "online" | "bank";

interface IPreConfirmationProps {
  onPay: (value: PaymentType) => void;
}

const PaymentType = ({ onPay }: IPreConfirmationProps) => {
  const [payment, setPayment] = useState<PaymentType>("online");

  return (
    <div className="">
      <div className="border-b-surface-nautral-modal-cover mx-4 border-b py-1 !pt-5">
        <span className="text-text-nautral-default text-xs">روش پرداخت</span>
      </div>
      <div className="mt-6.5 flex flex-col gap-3.5 px-4 leading-5">
        <span className="text-sm">روش پرداخت دلخواه خود را انتخاب کنید</span>

        <RadioGroup value={payment}>
          <div className="flex flex-col gap-2">
            <div
              className={cn(
                "cursor-pointer rounded-lg p-1",
                payment === "online"
                  ? "border-border-primary-default border"
                  : "",
              )}
              onClick={() => setPayment("online")}
            >
              <div className="bg-surface-nautral-transparent rounded px-1 py-3">
                <RadioGroupItem value="online" label="از طریق درگاه آنلاین" />
              </div>
            </div>

            <div
              className={cn(
                "border-border-primary-default cursor-pointer rounded-lg p-1",
                payment === "bank"
                  ? "border-border-primary-default border"
                  : "",
              )}
              onClick={() => setPayment("bank")}
            >
              <div className="bg-surface-nautral-transparent rounded px-1 py-3">
                <RadioGroupItem value="bank" label="از طریق درگاه آنلاین" />
              </div>
            </div>
          </div>
        </RadioGroup>
      </div>
      <div className="bg-surface-nautral-default-2 shadow-top-sm mt-3.5 px-3.5 pt-2 pb-4">
        <Button className="w-full" onClick={() => onPay(payment)}>
          پرداخت{" "}
        </Button>
      </div>
    </div>
  );
};

export default PaymentType;
