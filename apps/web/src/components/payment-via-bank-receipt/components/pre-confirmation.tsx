import { SEJAM_URL } from "@/constants/externalLinks";
import { Button } from "@workspace/ui/components/button";
import { Checkbox } from "@workspace/ui/components/checkbox";
import React from "react";

interface IPreConfirmationProps {
  onConfirm: () => void;
}

const PreConfirmation = ({ onConfirm }: IPreConfirmationProps) => {
  return (
    <div className="">
      <div className="border-b-surface-nautral-modal-cover mx-4 border-b py-1 !pt-5">
        <span className="text-text-nautral-default text-xs">پیش تائیدیه</span>
      </div>
      <div className="mt-6.5 flex flex-col gap-3 px-4 leading-5">
        <p className="text-[12px] font-bold">
          <span className="text-text-nautral-secondary">
            به موجب این رسید، گواهی می‌شود مبلغ{" "}
          </span>
          <span className="text-text-primary-default">2.000.000 ریال </span>
          <span className="text-text-nautral-secondary">به نام </span>
          <span className="text-text-primary-default">
            صندوق مشترک اندیشه صبا
          </span>
          <span className="text-text-nautral-secondary"> مورخ </span>
          <span className="text-text-primary-default">1404/02/24 </span>
          <span className="text-text-nautral-secondary">
            جهت صدور واحد سرمایه‌گذاری برای شخص سرمایه‌گذار با مشخصات ذیل پرداخت
            خواهد شد.
          </span>
        </p>

        <div>
          <p className="text-text-nautral-secondary text-[12px] font-bold">
            سرمایه‌گذار محترم، ثبت درخواست صدور به منزله قبول مفاد اساسنامه و
            امیدنامه صندوق و پذیرش تمامی ریسک‌های سرمایه‌گذاری احتمالی، مندرج در
            مستندات مذکور است.
          </p>
        </div>

        <div className="bg-surface-nautral-default-4 flex flex-col gap-2 rounded p-2">
          <div className="flex items-center gap-2">
            <span className="text-text-nautral-secondary text-xs">
              نام و نام خانوادگی:
            </span>
            <span className="text-text-nautral-default text-xs">
              محمد محمدی
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-text-nautral-secondary text-xs">کدملی:</span>
            <span className="text-text-nautral-default text-xs">002345132</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-text-nautral-secondary text-xs">
              تاریخ تولد:
            </span>
            <span className="text-text-nautral-default text-xs">
              1370/05/21
            </span>
          </div>
        </div>

        <div className="mt-1 text-[10px] flex gap-0.5">
          <span className="text-text-nautral-secondary">
            در صورت نیاز به اصلاح اطلاعات به
          </span>
          <a
            className="text-text-primary-default "
            href={SEJAM_URL}
            target="_blank"
          >
            سامانه سجام
          </a>
          <span className="text-text-nautral-secondary">مراجعه نمایید.</span>
        </div>

        <div className="mt-1">
          <Checkbox label="صحت اطلاعات فوق مورد تائید اینجانب می‌باشد." />
        </div>
      </div>
      <div className="bg-surface-nautral-default-2 shadow-top-sm mt-6 px-4 pt-2 pb-4">
        <Button className="w-full" onClick={onConfirm}>
          تائید و ادامه
        </Button>
      </div>
    </div>
  );
};

export default PreConfirmation;
