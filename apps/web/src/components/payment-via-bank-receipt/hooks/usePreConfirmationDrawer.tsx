"use client";
import { useResponsiveModal } from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import PreConfirmation from "../components/pre-confirmation";

const usePreConfirmDrawer = () => {
  const { openModal, closeModal } = useResponsiveModal();

  const id = `pre-confirm-modal-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  function onCloseModal() {
    closeModal(id);
  }

  const openPreConfirmModal = (onConfirm: () => void) => {
    return openModal({
      id,
      content: <PreConfirmation onConfirm={onConfirm} />,
    });
  };

  return { openPreConfirmModal, closeModal: onCloseModal };
};

export default usePreConfirmDrawer;
