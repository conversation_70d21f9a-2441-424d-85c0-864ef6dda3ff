"use client";
import { useResponsiveModal } from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import { DialogTitle } from "@workspace/ui/components/dialog";

const useFormSelectDrawer = () => {
  const { openModal, closeModal } = useResponsiveModal();

  const id = `select-modal-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  function onCloseModal() {
    closeModal(id);
  }

  const openSelectModal = (
    accounts: {
      name: string;
      accountNumber: string;
    }[],
    onSelect: (accountId: string) => void,
  ) => {
    return openModal({
      id,
      content: (
        <div className="p-3">
          <div>
            <DialogTitle className="border-b-surface-nautral-modal-cover mb-1 border-b pt-4 pb-2 text-xs font-bold">
              شماره حساب بانکی
            </DialogTitle>
            {accounts?.map((item) => (
              <div
                onClick={() => onSelect(item?.accountNumber)}
                key={item?.accountNumber}
                className="border-b-surface-nautral-modal-cover flex justify-between border-b p-2 py-4 text-sm last-of-type:border-none"
              >
                <div>{item?.name}</div>
                <div>{item?.accountNumber}</div>
              </div>
            ))}
          </div>
        </div>
      ),
    });
  };

  return { openSelectModal, closeModal: onCloseModal };
};

export default useFormSelectDrawer;
