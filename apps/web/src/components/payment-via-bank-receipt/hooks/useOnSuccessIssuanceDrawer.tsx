"use client";
import { useResponsiveModal } from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import { DrawerTitle } from "@workspace/ui/components/drawer";
import Check from "@/assets/icons/Check.svg";
import { Button } from "@workspace/ui/components/button";

const useOnSuccessIssuanceDrawer = () => {
  const { openModal, closeModal } = useResponsiveModal();

  const id = `success-issuance-modal-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  function onCloseModal() {
    closeModal(id);
  }

  const openSuccessIssuanceModal = (
    info: { name: string; value: string }[],
  ) => {
    return openModal({
      id,
      content: (
        <>
          <DrawerTitle />
          <div className="bg-background-nautral-body z-[51] -mt-2.5 flex h-screen w-screen flex-col items-center justify-between p-4 align-middle">
            <div className="flex w-full flex-col items-center justify-center pt-12 text-sm">
              <div className="bg-surface-success-default flex h-16 w-16 rounded-2xl">
                <Check className="text-icon-success-default m-auto w-8" />
              </div>
              <div className="text-text-success-default pt-4 pb-6 text-center">
                درخواست صدور با موفقیت ثبت شد
              </div>
              <div className="flex w-full flex-col gap-[14px]">
                {info?.map(({ name, value }) => (
                  <div className="flex justify-between">
                    <div className="text-text-nautral-secondary text-xs">
                      {name}
                    </div>
                    <div className="text-text-nautral-default text-xs">
                      {value}
                    </div>
                  </div>
                ))}
              </div>
              <div className="bg-surface-nautral-transparent text-text-nautral-secondary mt-[25px] rounded-sm p-3 text-center text-xs">
                تکمیل فرآیند خرید صندوق از زمان ثبت درخواست حداکثر تا 2 روز کاری
                زمان خواهد برد.
              </div>
            </div>
            <div className="flex w-full gap-2">
              <Button
                className="min-w-[140px] grow basis-[45%]"
                variant="fill"
                size="md"
                onClick={onCloseModal}
              >
                متوجه شدم
              </Button>
              <Button
                className="min-w-[140px] grow basis-[45%]"
                variant="outline"
                size="md"
                onClick={onCloseModal}
              >
                پیگیری درخواست
              </Button>
            </div>
          </div>
        </>
      ),
    });
  };

  return { openSuccessIssuanceModal, closeModal: onCloseModal };
};

export default useOnSuccessIssuanceDrawer;
