"use client";
import { useResponsiveModal } from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import PaymentViaBankReceipt from "@/components/payment-via-bank-receipt/payment-via-bank-receipt";
import useDatePickerDrawer from "@/hooks/useDatePickerDrawer";
import useFormSelectDrawer from "@/components/payment-via-bank-receipt/hooks/useFormSelectDrawer";
import useOnSuccessIssuanceDrawer from "@/components/payment-via-bank-receipt/hooks/useOnSuccessIssuanceDrawer";

const usePaymentViaBankReceipt = () => {
  const { openModal, closeModal } = useResponsiveModal();
  const { openSelectModal, closeModal: closeSelectModal } =
    useFormSelectDrawer();
  const { openDatePickerModal } = useDatePickerDrawer();
  const { openSuccessIssuanceModal } = useOnSuccessIssuanceDrawer();

  const openPaymentViaBankReceipt = () => {
    const id = `payment-modal-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    return openModal({
      id,
      content: (
        <PaymentViaBankReceipt
          onClose={() => closeModal(id)}
          openSelectModal={openSelectModal}
          closeSelectModal={() => closeSelectModal()}
          openDatePickerModal={openDatePickerModal}
          onSuccessSubmit={openSuccessIssuanceModal}
        />
      ),
      isFullScreen: true,
    });
  };

  return { openPaymentViaBankReceipt };
};

export default usePaymentViaBankReceipt;
