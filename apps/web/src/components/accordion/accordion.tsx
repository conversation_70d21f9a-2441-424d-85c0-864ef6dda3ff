"use client";

import * as React from "react";
import * as AccordionPrimitive from "@radix-ui/react-accordion";
import ChevronDownIcon from "@/assets/icons/arrow-down.svg";

import { cn } from "@workspace/ui/lib/utils";

interface AccordionTriggerProps
  extends React.ComponentProps<typeof AccordionPrimitive.Trigger> {
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
}

function Accordion({
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Root>) {
  return <AccordionPrimitive.Root data-slot="accordion" {...props} />;
}

function AccordionItem({
  className,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Item>) {
  return (
    <AccordionPrimitive.Item
      data-slot="accordion-item"
      className={cn("bg-surface-nautral-default-4 rounded-lg px-4", className)}
      {...props}
    />
  );
}

function AccordionTrigger({
  className,
  children,
  startAdornment,
  endAdornment,
  ...props
}: AccordionTriggerProps) {
  return (
    <AccordionPrimitive.Header className="flex">
      <AccordionPrimitive.Trigger
        data-slot="accordion-trigger"
        className={cn(
          "flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>div>svg]:rotate-180",
          className,
        )}
        {...props}
      >
        <div className="flex items-center gap-2">
          <div>{startAdornment}</div>
          {children}
        </div>
        <div className="flex gap-2">
          {endAdornment}
          <ChevronDownIcon className="size-4 shrink-0 translate-y-1 cursor-pointer transition-transform duration-200" />
        </div>
      </AccordionPrimitive.Trigger>
    </AccordionPrimitive.Header>
  );
}

function AccordionContent({
  className,
  children,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Content>) {
  return (
    <AccordionPrimitive.Content
      data-slot="accordion-content"
      className="data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm"
      {...props}
    >
      <div className={cn("pt-0 pb-4", className)}>{children}</div>
    </AccordionPrimitive.Content>
  );
}

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent };
