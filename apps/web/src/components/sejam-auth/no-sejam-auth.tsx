"use client";
import { DrawerHeader, DrawerTitle } from "@workspace/ui/components/drawer";
import { Button } from "@workspace/ui/components/button";

const NoSejamAuth = ({ openModal }: { openModal: () => void }) => {
  return (
    <div className="flex flex-col">
      <DrawerHeader>
        <DrawerTitle className="border-b-surface-nautral-modal-cover border-b pt-2.5 pb-3 text-xs">
          احراز هویت سجام
        </DrawerTitle>
      </DrawerHeader>
      <div className="flex flex-col p-4">
        <div className="pb-[58px] text-sm">
          برای سرمایه‌گذاری در صندوق‌های صبا تامین، استعلام احراز هویت از سامانه
          سجام الزامی است.
        </div>
        <Button
          className="mt-4 w-full"
          variant="fill"
          size="md"
          onClick={() => openModal()}
        >
          احراز هویت
        </Button>
      </div>
    </div>
  );
};

export default NoSejamAuth;
