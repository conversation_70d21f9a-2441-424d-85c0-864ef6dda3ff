"use client";
import { useResponsiveModal } from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import NoSejamAuth from "@/components/sejam-auth/no-sejam-auth";
import useSejamAuthDrawer from "@/components/sejam-auth/useSejamAuthDrawer";

const useNoSejamAuthDrawer = () => {
  const { openModal } = useResponsiveModal();
  const { openSejamModal } = useSejamAuthDrawer();

  const openNoSejamModal = () => {
    return openModal({
      id: `pre-sejam-modal-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      content: <NoSejamAuth openModal={openSejamModal} />,
    });
  };

  return { openNoSejamModal };
};

export default useNoSejamAuthDrawer;
