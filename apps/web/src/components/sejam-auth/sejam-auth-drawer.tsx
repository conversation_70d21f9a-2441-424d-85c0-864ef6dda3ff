"use client";
import { useState } from "react";
import {
  NoSejam,
  SejamInfo,
  SejamLogin,
  CreatePassword,
  ConfirmPassword,
  SejamOtp,
} from "src/components/sejam-auth/components";
import { handleErrorText, StepsEnum } from "@/components/sejam-auth/utils";
import { UserSejamProfileResponse } from "@workspace/investment-api/auto-generated/models";
import {
  sejamProfileInquiryShow,
  useSejamIsSejami,
  useSejamKycOTP,
  useSejamProfileInquiry,
  useSejamSetAsCustomerProfile,
} from "@workspace/investment-api/auto-generated/apis/sejam/sejam";
import { ICreatPasswordSubmit } from "@/components/create-password/types";
import { useFinancialInstitutesGetAll } from "@workspace/investment-api/auto-generated/apis/financial-institutes/financial-institutes";
import { toast } from "@workspace/ui/components/toast";

const SejamAuthDrawer = () => {
  const [step, setStep] = useState(1);

  const { mutateAsync: mutateSejamSetAsCustomer } =
    useSejamSetAsCustomerProfile();
  const { mutateAsync: mutateSejamProfile } = useSejamProfileInquiry();
  const { data: financialInstitutes } = useFinancialInstitutesGetAll();
  const { mutateAsync: postSejamKycOtp } = useSejamKycOTP();
  const { mutateAsync: postIsSejam } = useSejamIsSejami();
  const [info, setInfo] = useState({
    nationalCode: "",
    financialInstituteId: "",
  });
  const [sejamData, setSejamData] = useState<UserSejamProfileResponse | null>(
    null,
  );

  const handlePasswordSubmit = async (data: ICreatPasswordSubmit) => {
    return mutateSejamSetAsCustomer({
      data: {
        ...info,
        ...data,
      },
    })
      .then(() => setStep(StepsEnum.CONFIRM_PASSWORD))
      .catch((error) => {
        toast.error(handleErrorText(error?.response?.data?.errorCode));
      });
  };

  const handleSejamSubmit = () => {
    setStep(StepsEnum.MAKE_PASSWORD);
  };

  const sendOtp = async (nationalCode?: string) => {
    return postSejamKycOtp({
      data: {
        nationalCode: nationalCode || info?.nationalCode,
        financialInstituteId:
          financialInstitutes?.data?.[0]?.id || info?.financialInstituteId,
      },
    }).catch((error) => {
      toast.error(
        error?.response?.data?.errorMessage || "لطفا چند لحظه دیگر تلاش کنید.",
      );
    });
  };

  const onResend = async () => {
    return sendOtp();
  };

  const handleSejamLogin = async (values: { nationalCode: string }) => {
    setInfo({
      financialInstituteId: financialInstitutes?.data?.[0]?.id!,
      nationalCode: values.nationalCode,
    });
    return postIsSejam({
      data: {
        nationalCode: values.nationalCode,
        financialInstituteId: financialInstitutes?.data?.[0]?.id,
      },
    })
      ?.then((res) => {
        if (res?.data) {
          setStep(StepsEnum.OTP);
          sendOtp(values?.nationalCode);
        } else {
          setStep(StepsEnum.NO_SEJAM);
        }
      })
      .catch((error) => {
        toast.error(
          error?.response?.data?.errorMessage ||
            "لطفا چند لحظه دیگر تلاش کنید.",
        );
      });
  };

  const handleSejamOtpSubmit = async (otp: string) => {
    return mutateSejamProfile({ data: { ...info, kycOtpCode: otp } })
      ?.then((e) => {
        setInfo((prev) => ({ ...prev, SecretKey: e?.data }));
        sejamProfileInquiryShow({
          FinancialInstituteId: info?.financialInstituteId,
          SecretKey: e?.data,
        })
          .then((e) => {
            setSejamData(e?.data!);
            setStep(StepsEnum.CHECK_INFORMATION);
          })
          .catch((error) => {
            toast.error(
              error?.response?.data?.errorMessage ||
                "لطفا چند لحظه دیگر تلاش کنید.",
            );
          });
      })
      .catch((error) => {
        toast.error(
          error?.response?.data?.errorMessage ||
            "لطفا چند لحظه دیگر تلاش کنید.",
        );
      });
  };

  return (
    <>
      {step === StepsEnum.NO_SEJAM && <NoSejam {...{ info }} />}
      {step === StepsEnum.START && <SejamLogin onSubmit={handleSejamLogin} />}
      {step === StepsEnum.OTP && (
        <SejamOtp
          {...{ info, setStep, onResend }}
          onSubmit={handleSejamOtpSubmit}
        />
      )}
      {step === StepsEnum.CHECK_INFORMATION && (
        <SejamInfo {...{ sejamData }} onSubmit={handleSejamSubmit} />
      )}
      {step === StepsEnum.MAKE_PASSWORD && (
        <CreatePassword onSubmit={handlePasswordSubmit} />
      )}
      {step === StepsEnum.CONFIRM_PASSWORD && <ConfirmPassword />}
    </>
  );
};

export default SejamAuthDrawer;
