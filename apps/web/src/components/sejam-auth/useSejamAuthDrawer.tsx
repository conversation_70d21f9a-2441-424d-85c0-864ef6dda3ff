"use client";
import { useResponsiveModal } from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import SejamAuthDrawer from "@/components/sejam-auth/sejam-auth-drawer";

const useSejamAuthDrawer = () => {
  const { openModal } = useResponsiveModal();

  const openSejamModal = () => {
    return openModal({
      id: `sejam-modal-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      content: <SejamAuthDrawer />,
    });
  };

  return { openSejamModal };
};

export default useSejamAuthDrawer;
