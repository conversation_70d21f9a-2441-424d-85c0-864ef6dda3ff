import { DrawerHeader, DrawerTitle } from "@workspace/ui/components/drawer";
import React from "react";
import { Button } from "@workspace/ui/components/button";
import LinkIcon from "@/assets/icons/link.svg";
import Link from "next/link";
import { SEJAM_SIGNUP_URL } from "@/constants/externalLinks";

interface INoSejam {
  info?: { financialInstituteId: string; nationalCode: string };
}

function NoSejam({ info }: INoSejam) {
  return (
    <>
      <DrawerHeader>
        <DrawerTitle className="pt-2.5 text-xs">احراز هویت سجام</DrawerTitle>
      </DrawerHeader>
      <div className="flex flex-col p-4">
        <div className="pb-[58px] text-sm">
          شما با کدملی{" "}
          <span className="px-px underline">{info?.nationalCode}</span>
          در سامانه سجام ثبت‌نام نکرده‌اید.
        </div>
        <Link className="grid" href={SEJAM_SIGNUP_URL}>
          <Button
            variant="fill"
            size="md"
            type="submit"
            form="login-form w-full"
          >
            <LinkIcon />
            ثبت نام در سجام
          </Button>
        </Link>
      </div>
    </>
  );
}

export default NoSejam;
