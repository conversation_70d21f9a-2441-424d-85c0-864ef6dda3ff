import { dateConverter } from "@/utils/dateConverter";
// import { InfoKeysAndLabels } from "./types";

// export const infoKeysAndLabel: InfoKeysAndLabels[] = [
//   {
//     key: "privatePerson",
//     label: "اطلاعات شخصی",
//     children: [
//       {
//         key: "firstName",
//         label: "نام و نام خانوادگی",
//         formatter: (data) =>
//           data?.privatePerson?.lastName
//             ? `${data?.privatePerson?.firstName} ${data?.privatePerson?.lastName}`
//             : "",
//       },
//       {
//         key: "fatherName",
//         label: "نام پدر",
//         formatter: (data) => data?.privatePerson?.fatherName,
//       },
//       {
//         key: "uniqueIdentifier",
//         label: "کدملی",
//         formatter: (data) => data?.uniqueIdentifier,
//       },
//       {
//         key: "birthDate",
//         label: "تاریخ تولد",
//         formatter: (data) =>
//           dateConverter(data?.privatePerson?.birthDate!).format("YYYY/MM/DD"),
//       },
//       {
//         key: "tradingCodes",
//         label: "کد سهامداری",
//         formatter: (data) => data?.tradingCodes?.[0]?.code,
//       },
//       {
//         key: "mobile",
//         label: "شماره موبایل",
//         formatter: (data) => data?.mobile,
//       },
//     ],
//   },
//   {
//     key: "accounts",
//     label: "اطلاعات حساب بانکی",
//     children: [
//       {
//         key: "accounts",
//         label: "شماره حساب",
//         formatter: (data) => data?.accounts?.[0]?.accountNumber,
//       },
//       {
//         key: "sheba",
//         label: "شماره شبا",
//         formatter: (data) => data?.accounts?.[0]?.sheba,
//       },
//       {
//         key: "bank",
//         label: "نام بانک",
//         formatter: (data: any) => data?.accounts?.[0]?.bank?.name,
//       },
//       {
//         key: "branchCity",
//         label: "شعبه",
//         formatter: (data) => data?.accounts?.[0]?.branchName,
//       },
//     ],
//   },
// ];

export const infoKeysAndLabe = [];
