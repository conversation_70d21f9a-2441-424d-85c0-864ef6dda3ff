import { UserSejamProfileResponse } from "@workspace/investment-api/auto-generated/models";

export interface ISejamInfo {
  sejamData: UserSejamProfileResponse | null;
  onSubmit: () => void;
}

export interface InfoKeysAndLabels {
  key: string;
  label: string;
  children?: {
    key: string;
    label: string;
    formatter: (
      data: UserSejamProfileResponse,
    ) => number | string | null | undefined;
  }[];
}
