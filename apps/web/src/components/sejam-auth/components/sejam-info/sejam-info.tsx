import { DrawerHeader, DrawerTitle } from "@workspace/ui/components/drawer";
import { Button } from "@workspace/ui/components/button";
import SejamDetails from "./sejam-details";
import { ISejamInfo } from "./types";

const sejamInfo = ({ sejamData, onSubmit }: ISejamInfo) => (
  <>
    <DrawerHeader className="pt-[27px] pb-0">
      <DrawerTitle className="border-b-surface-nautral-modal-cover text-text-nautral-default border-b pb-2 text-xs">
        بررسی اطلاعات
      </DrawerTitle>
    </DrawerHeader>
    <SejamDetails data={sejamData} />
    <div className="flex w-full px-4 pb-4">
      <Button className="w-full" variant="fill" size="md" onClick={onSubmit}>
        تایید اطلاعات
      </Button>
    </div>
  </>
);

export default sejamInfo;
