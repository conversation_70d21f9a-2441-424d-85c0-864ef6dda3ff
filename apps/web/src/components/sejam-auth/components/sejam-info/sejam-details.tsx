import { UserSejamProfileResponse } from "@workspace/investment-api/auto-generated/models";
// import { infoKeysAndLabel } from "@/components/sejam-auth/components/sejam-info/utils";

function SejamDetails({ data }: { data: UserSejamProfileResponse | null }) {
  return (
    <div className="px-4 pt-5 pb-[12px]">
      {/* {infoKeysAndLabel?.map(({ key, label, children }) => (
        <div
          key={key}
          className="bg-surface-nautral-default-4 mb-[10px] rounded-lg p-4"
        >
          <div className="text-text-nautral-default pb-0.5 text-xs font-bold">
            {label}
          </div>
          {children?.map((item) => (
            <div key={item?.key} className="flex pt-1.5 pb-0.5">
              <div className="text-text-nautral-secondary text-xs">
                {item?.label}:
              </div>
              <div className="text-text-nautral-default pr-2 text-xs">
                {item.formatter(data!)}
              </div>
            </div>
          ))}
        </div>
      ))} */}
    </div>
  );
}

export default SejamDetails;
