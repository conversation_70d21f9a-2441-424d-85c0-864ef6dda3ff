import { Button } from "@workspace/ui/components/button";
import Check from "@/assets/icons/Check.svg";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { useState } from "react";
import { useLogout } from "@/hooks/useLogout";
import { DrawerTitle } from "@workspace/ui/components/drawer";

const ConfirmPassword = () => {
  const [isDisabled, setIsDisabled] = useState(true);
  const { logout } = useLogout();

  const handleCheckBoxClick = () => {
    setIsDisabled((prev) => !prev);
  };

  return (
    <>
      <DrawerTitle />
      <div className="bg-background-nautral-body z-[51] -mt-2.5 flex h-screen w-screen flex-col items-center justify-between p-4 align-middle">
        <div className="flex w-full flex-col items-center justify-center pt-12 text-center text-sm">
          <div className="bg-surface-success-default flex h-16 w-16 rounded-2xl">
            <Check className="text-icon-success-default m-auto w-8" />
          </div>
          <div className="text-text-success-default pt-4 pb-5">
            رمزعبور با موفقیت ایجاد شد
          </div>
          <div className="text-text-nautral-default">
            از این پس صرفا با شماره موبایلی که پیامک سجام برایتان ارسال شد و رمز
            عبور جدید وارد شوید.
          </div>
        </div>
        <div className="flex w-full flex-col">
          <div className="mr-0.5 mb-[26px]">
            <Checkbox
              size="lg"
              label="متن بالا را مطالعه کردم."
              onClick={handleCheckBoxClick}
            />
          </div>
          <Button
            variant="fill"
            size="md"
            className="w-full"
            disabled={isDisabled}
            onClick={logout}
          >
            ورود با رمز جدید
          </Button>
        </div>
      </div>
    </>
  );
};

export default ConfirmPassword;
