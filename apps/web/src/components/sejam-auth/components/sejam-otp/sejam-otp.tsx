import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { InputNumber } from "@workspace/ui/components/input-number";
import { WRONG_CODE_MESSAGE } from "@/app/(auth)/utils";
import OtpTimer from "@/app/(auth)/components/login-by-otp/otp-timer";
import { Button } from "@workspace/ui/components/button";
import { Loading } from "@workspace/ui/components/loading";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { ISejamOtp } from "./types";
import { StepsEnum } from "@/components/sejam-auth/utils";
import { SEJAM_URL } from "@/constants/externalLinks";
import { DialogTitle } from "@workspace/ui/components/dialog";
const formSchema = z.object({
  otp: z.string().nonempty(" کد را وارد کنید."),
});
const MAX_OTP_NUMBER = 5;

const SejamOtp = ({ info, onSubmit, setStep, onResend }: ISejamOtp) => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      otp: "",
    },
  });
  const otp = form.watch("otp");
  const formState = form?.formState as unknown as { values: { otp: string } };
  function onFormSubmit(values: z.infer<typeof formSchema>) {
    return onSubmit(values?.otp);
  }

  useEffect(() => {
    if (otp.length === MAX_OTP_NUMBER) {
      onFormSubmit({ otp });
    }
  }, [otp]);

  return (
    <div className="mt-[30px] flex flex-1 flex-col justify-between px-4 pb-4">
      <DialogTitle />
      <div className="flex shrink-0 flex-col">
        <div className="mb-[18px]">
          <p className="text-text-nautral-default flex items-center text-sm">
            رمز یکبار مصرف به شماره موبایل ثبت شده در سجام برای کد ملی{" "}
            {info?.nationalCode} ارسال شد.
          </p>
        </div>

        <Form {...form}>
          <form
            id="sejam-otp-form"
            onSubmit={form.handleSubmit(onFormSubmit)}
            className="flex flex-col justify-between"
          >
            <div className="flex flex-col justify-between">
              <FormField
                control={form.control}
                name="otp"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <InputNumber
                        size="lg"
                        commaSeparated={false}
                        {...field}
                        placeholder="-----"
                        autoFocus
                        maxLength={MAX_OTP_NUMBER}
                        onChange={(e) => {
                          field.onChange(e);
                          if (
                            fieldState?.error?.message === WRONG_CODE_MESSAGE
                          ) {
                            form.clearErrors("otp");
                          }
                        }}
                        rootClassName="!ps-2 !pe-5"
                        className="w-full text-center tracking-[14px] placeholder:text-center placeholder:tracking-[14px]"
                        dir="ltr"
                        helperText={
                          fieldState?.error?.message !== WRONG_CODE_MESSAGE
                            ? fieldState?.error?.message
                            : ""
                        }
                        aria-invalid={!!fieldState?.error?.message}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>

        <div className="text-text-nautral-secondary pt-[9px] pb-[18px] text-[10px]">
          در صورت نیاز به اصلاح شماره موبایل خود لطفا به{" "}
          <a
            target="_blank"
            className="text-text-primary-default"
            href={SEJAM_URL}
          >
            {" "}
            سامانه سجام{" "}
          </a>
          مراجعه نمایید.
        </div>

        <div className="pr-0.5 pb-[55px]">
          <OtpTimer
            expirationDate={Date.now() + 2 * 60 * 1000}
            otpSendDate={Date.now()}
            onResend={() => onResend()}
            isLoading={form.formState.isSubmitting}
          />
        </div>
      </div>
      <div className="flex min-h-24 shrink-0 flex-col justify-self-end">
        <Button
          className="border-border-primary-default text-text-primary-default mb-4"
          variant="outline"
          size="md"
          onClick={() => {
            setStep(StepsEnum.START);
          }}
        >
          اصلاح کدملی
        </Button>
        <Button
          variant="fill"
          size="md"
          type="submit"
          form="sejam-otp-form"
          disabled={Number(formState?.values?.otp?.length) !== 5}
          endAdornment={form.formState.isSubmitting && <Loading size="sm" />}
        >
          تایید و ادامه
        </Button>
      </div>
    </div>
  );
};

export default SejamOtp;
