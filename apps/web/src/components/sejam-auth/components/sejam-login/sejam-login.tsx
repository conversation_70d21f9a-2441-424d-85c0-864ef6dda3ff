import { useRef } from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodR<PERSON>olver } from "@hookform/resolvers/zod";
import nationalCodeValidator from "@/utils/nationalCodeValidator";
import { DrawerHeader, DrawerTitle } from "@workspace/ui/components/drawer";
import { Button } from "@workspace/ui/components/button";
import { Loading } from "@workspace/ui/components/loading";
import { ISejamLogin } from "./types";

const formSchema = z.object({
  nationalCode: z
    .string()
    .nonempty("کد ملی را وارد کنید")
    .refine((value) => nationalCodeValidator(value), {
      message: "کد ملی وارد شده صحیح نیست!",
    }),
});

const SejamLogin = ({ onSubmit }: ISejamLogin) => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      nationalCode: "",
    },
  });

  function onFormSubmit(values: z.infer<typeof formSchema>) {
    return onSubmit(values);
  }

  return (
    <div className="px-4 pb-4">
      <DrawerHeader className="px-0 pb-[22px]">
        <DrawerTitle className="border-b-surface-nautral-modal-cover mb-[22px] border-b pt-2 pb-[5px] text-xs">
          احراز هویت سجام
        </DrawerTitle>
        <div className="text-sm">
          برای سرمایه‌گذاری در صندوق‌های صبا تامین، استعلام احراز هویت از سامانه
          سجام الزامی است.
        </div>
      </DrawerHeader>
      <Form {...form}>
        <form
          id="sejam-login-form"
          onSubmit={form.handleSubmit(onFormSubmit)}
          className="flex flex-col justify-between pt-3 pb-10"
        >
          <div className="flex flex-col justify-between gap-4">
            <FormField
              control={form.control}
              name="nationalCode"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      size="lg"
                      title="کد ملی"
                      {...field}
                      helperText={fieldState?.error?.message}
                      aria-invalid={!!fieldState?.error?.message}
                      name="nationalCode"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </form>
      </Form>

      <Button
        className="mt-4 w-full"
        variant="fill"
        size="md"
        type="submit"
        form="sejam-login-form"
        disabled={form.formState.isSubmitting}
        endAdornment={form.formState.isSubmitting && <Loading size="sm" />}
      >
        استعلام احراز هویت
      </Button>
    </div>
  );
};

export default SejamLogin;
