import { ProgressBar } from "@/components/progress-bar";
import type { <PERSON>a, StoryObj } from "@storybook/react";

const meta: Meta<typeof ProgressBar> = {
  title: "Web/progress-bar",
  component: ProgressBar,
  parameters: {
    layout: "centered",
  },
  // tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div
        className="bg-surface-nautral-active container m-8 max-w-sm p-8"
        dir="rtl"
      >
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof ProgressBar>;

export const Primary: Story = {
  args: {
    value: 20,
  },
};
