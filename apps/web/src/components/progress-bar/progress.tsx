"use client";

import * as React from "react";
import * as ProgressPrimitive from "@radix-ui/react-progress";
import { cn } from "@workspace/ui/lib/utils";

function Progress({
  className,
  value = 0,
  ...props
}: React.ComponentProps<typeof ProgressPrimitive.Root>) {
  const isComplete = value === 100;
  return (
    <ProgressPrimitive.Root
      data-slot="progress"
      className={cn(
        "bg-surface-nautral-default-2 !relative flex h-4 w-full justify-between overflow-hidden rounded-full",
        className,
      )}
      {...props}
    >
      <div className="bg-surface-nautral-default-1 absolute left-[33.4%] z-10 my-0.5 h-[calc(100%-4px)] w-[1px]" />

      <div className="bg-surface-nautral-default-1 absolute left-[66.5%] z-10 my-0.5 h-[calc(100%-4px)] h-full w-[1px]" />

      <ProgressPrimitive.Indicator
        data-slot="progress-indicator"
        className={cn(
          "bg-button-primary-default absolute top-0 bottom-0 left-0.5 my-0.5 flex-1 rounded-l-full transition-all duration-500 ease-in-out",
          isComplete ? "right-0.5 rounded-r-full" : "",
        )}
        style={{
          width: isComplete ? `calc(100% - 4px)` : `${value}%`,
        }}
      />
    </ProgressPrimitive.Root>
  );
}

export { Progress };
