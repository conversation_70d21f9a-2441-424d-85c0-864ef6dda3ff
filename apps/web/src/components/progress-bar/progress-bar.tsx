"use client";

import { Progress } from "@/components/progress-bar/progress";
import { IProgressBarProps, Status } from "@/components/progress-bar/types";
import { cn } from "@workspace/ui/lib/utils";
import React, { useEffect, useState } from "react";

const ANIMATION_LOAD_TIME = 500;

const statusItems: Record<Status, { title: string; progress: number }> = {
  high: { title: "زیاد", progress: 100 },
  medium: { title: "متوسط", progress: 66 },
  low: { title: "کم", progress: 33 },
};

function ProgressBar({ value, status }: IProgressBarProps) {
  const [progress, setProgress] = useState(value || 0);

  useEffect(() => {
    if (status && statusItems[status]) {
      const timeout = setTimeout(() => {
        setProgress(statusItems[status].progress);
      }, ANIMATION_LOAD_TIME);
      return () => clearTimeout(timeout);
    }
  }, [status]);

  return (
    <div className="flex flex-col gap-1">
      <div className="flex items-center justify-around px-1.5">
        {Object.entries(statusItems).map(([key, { title }]) => (
          <p
            key={key}
            className={cn(
              "text-[10px]",
              status === key
                ? "text-text-primary-default font-bold"
                : "text-text-nautral-default",
            )}
          >
            {title}
          </p>
        ))}
      </div>
      <Progress value={progress} />
    </div>
  );
}

export default ProgressBar;
