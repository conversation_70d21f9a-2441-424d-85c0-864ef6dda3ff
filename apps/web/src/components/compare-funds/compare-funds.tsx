import React, { useEffect, useMemo, useState } from "react";
import FundChart from "@/components/fund-chart";
import SwitchTabs from "@workspace/ui/components/switchTabs";
import { FILTER_TIME, generateSeries, periodFilterItems } from "./utils";
import ArrowDown from "@workspace/ui/assets/icons/arrow-down.svg";
import { useAggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank } from "@workspace/investment-api/auto-generated/apis/aggregated-return-reports/aggregated-return-reports";
import { format } from "date-fns";
import { subtractMonthFromDate } from "@/utils/dateConverter";
// Todo: check new type
// import { GetAllFinancialInstitutesResponse } from "@workspace/investment-api/auto-generated/models";
import useCompareSelectDrawer from "@/components/compare-funds/useCompareSelectDrawer";
import { useFinancialInstitutesGetAll } from "@workspace/investment-api/auto-generated/apis/financial-institutes/financial-institutes";
import { Skeleton } from "@workspace/ui/components/skeleton";

const CompareFunds = () => {
  const { data: financialInstitutes, isLoading } =
    useFinancialInstitutesGetAll();
  const [selectedFund, setSelectedFund] = useState<any | null>(null);
  const [fromDate, setFromDate] = useState(
    format(subtractMonthFromDate(FILTER_TIME.Yearly), "yyyy-MM-dd"),
  );
  function handleFundChange(v: any) {
    setSelectedFund(v);
  }

  useEffect(() => {
    if (financialInstitutes?.data?.[0])
      setSelectedFund(financialInstitutes?.data?.[0]);
  }, [financialInstitutes]);

  const { data } =
    useAggregatedReturnReportsGetAggregatedReturnOfInstituteComparedToBank({
      FinancialInstituteId: selectedFund?.id,
      ToDate: format(new Date(), "yyyy-MM-dd"),
      FromDate: fromDate,
    });
  const { openCompareSelectModal } = useCompareSelectDrawer();

  const series = useMemo(() => {
    return generateSeries(
      data?.data?.bankReturns,
      data?.data?.instituteReturns,
    );
  }, [data]);

  return (
    <div className="bg-surface-nautral-transparent p-4">
      {isLoading ? (
        <Skeleton className="h-[180px]" />
      ) : (
        <>
          <div
            className="flex items-center justify-between pb-2.5"
            onClick={() => openCompareSelectModal(handleFundChange)}
          >
            <div className="text-text-nautral-default text-xs font-bold">
              مقایسه با
            </div>
            <div className="bg-button-primary-default text-text-on-surface-default cursor-pointer rounded-xl px-3 py-1 text-xs">
              {selectedFund?.fullName}
              <ArrowDown className="mr-2 inline size-3.5" />
            </div>
          </div>
          <FundChart series={series} />
        </>
      )}

      <div className="pt-2">
        <SwitchTabs
          tabs={periodFilterItems}
          activeItemId={fromDate}
          onChange={(e) => {
            setFromDate(e as string);
          }}
        />
      </div>
    </div>
  );
};

export default CompareFunds;
