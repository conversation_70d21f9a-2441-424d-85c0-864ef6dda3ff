"use client";
import { useResponsiveModal } from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import { DialogHeader, DialogTitle } from "@workspace/ui/components/dialog";
import * as React from "react";
import Graph from "@/assets/icons/graph.svg";
import { useFinancialInstitutesGetAll } from "@workspace/investment-api/auto-generated/apis/financial-institutes/financial-institutes";
// Todo: check new type
// import { GetAllFinancialInstitutesResponse } from "@workspace/investment-api/auto-generated/models";

const useCompareSelectDrawer = () => {
  const { openModal, closeModal } = useResponsiveModal();
  const { data: financialInstitutes } = useFinancialInstitutesGetAll();
  const id = `compare-select-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  const openCompareSelectModal = (
    // Todo: check new type
    handleFundChange: (option: any) => void,
  ) => {
    const modalContent = (
      <>
        <DialogHeader>
          <DialogTitle className="border-b-surface-nautral-modal-cover mb-4 flex justify-start border-b px-4 pt-[26px] pb-2">
            <Graph className="ml-2 h-4 w-4" />
            <div className="text-text-nautral-default text-xs">
              {" "}
              مقایسه بازدهی با
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="cursor-pointer px-4 pt-2 pb-6">
          {financialInstitutes?.data?.map((option) => (
            <div
              onClick={() => {
                handleFundChange(option);
                closeModal(id);
              }}
            >
              <img
                alt="logo"
                src={`${option?.logo}`}
                className="inline h-6 w-auto"
              />
              {option?.fullName}
            </div>
          ))}
        </div>
      </>
    );

    return openModal({
      id,
      content: modalContent,
    });
  };

  return { openCompareSelectModal };
};

export default useCompareSelectDrawer;
