import { subtractMonthFromDate } from "@/utils/dateConverter";
import { AggregatedReturnDto } from "@workspace/investment-api/auto-generated/models";
import { GradientColorStopObject } from "highcharts";
import { format } from "date-fns";

export enum FILTER_TIME {
  Yearly = 12,
  Month6 = 6,
  Month3 = 3,
  Month1 = 1,
}

export const periodFilterItems = [
  {
    id: format(subtractMonthFromDate(FILTER_TIME.Month1), "yyyy-MM-dd"),
    label: "1 ماه",
  },
  {
    id: format(subtractMonthFromDate(FILTER_TIME.Month3), "yyyy-MM-dd"),
    label: "3 ماه",
  },
  {
    id: format(subtractMonthFromDate(FILTER_TIME.Month6), "yyyy-MM-dd"),
    label: "6 ماه",
  },
  {
    id: format(subtractMonthFromDate(FILTER_TIME.Yearly), "yyyy-MM-dd"),
    label: "1 سال",
  },
  {
    id: "",
    label: "از ابتدا",
  },
];

export const chartTypeItems = [
  {
    id: "nav",
    label: "NAV صدور/ابطال",
  },
  {
    id: "efficiency",
    label: "بازدهی",
  },
];

export function generateSeries(
  fund?: AggregatedReturnDto[],
  bonds?: AggregatedReturnDto[],
) {
  return [
    {
      type: "areaspline" as "streamgraph",
      name: "سود سپرده بانکی",
      data: fund?.map((item) => ({ x: item?.date, y: item?.return })) as any,
      color: "#5E8CE1",
      legendIndex: 1,
      fillColor: {
        linearGradient: { x1: 0, x2: 0, y1: 0, y2: 300 },
        stops: [
          [0, "rgba(103, 154, 247, 0.4)"],
          [1, "rgba(103, 154, 247, 0.1)"],
        ] as GradientColorStopObject[],
      },
      marker: {
        enabled: false,
      },
    },
    {
      type: "spline" as "streamgraph",
      name: "صندوق",
      color: "#FF928A",
      legendIndex: 0,
      data: bonds?.map((item) => ({ x: item?.date, y: item?.return })) as any,
    },
  ];
}
