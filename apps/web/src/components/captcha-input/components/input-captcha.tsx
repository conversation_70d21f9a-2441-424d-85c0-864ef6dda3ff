import React, {
  forwardRef,
  useImperativeHandle,
  useState,
  useRef,
} from "react";
import { Input, InputProps } from "@workspace/ui/components/input";
import Reload from "@/assets/icons/reload.svg";
import { Loading } from "@workspace/ui/components/loading";
import { ApiResultOfRequestCaptchaOutputDTO } from "@workspace/investment-api/auto-generated/models";

interface IInputCaptchaProps extends InputProps {
  captchaData?: ApiResultOfRequestCaptchaOutputDTO;
  onRefresh: () => void;
  onInputChange: (value: string) => void;
  isLoading?: boolean;
  error?: string;
  restProps?: InputProps;
}

// ✨ Imperative handle type
export interface InputCaptchaRef {
  setValue: (value: string) => void;
  blur?: () => void;
}

export const InputCaptcha = forwardRef<InputCaptchaRef, IInputCaptchaProps>(
  (
    { captchaData, onRefresh, onInputChange, isLoading, error, ...restProps },
    ref,
  ) => {
    const [inputValue, setInputValue] = useState("");
    const inputRef = useRef<HTMLInputElement>(null);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setInputValue(value);
      onInputChange(value);
    };

    // ✨ Expose imperative methods
    useImperativeHandle(ref, () => ({
      setValue: (value: string) => {
        setInputValue(value);
        onInputChange(value);
      },
      blur: () => {
        inputRef.current?.blur();
      },
    }));

    return (
      <div className="flex items-center gap-2">
        <Input
          {...restProps}
          ref={inputRef}
          size="lg"
          maxLength={6}
          value={inputValue}
          onChange={handleInputChange}
          helperText={error}
          aria-invalid={!!error}
          name="captchaInput"
          rootClassName="pe-0.5"
          endAdornment={
            captchaData?.data?.captchaImage && (
              <img
                src={captchaData.data.captchaImage}
                alt="CAPTCHA"
                className="h-12 w-32 rounded"
              />
            )
          }
        />
        {isLoading ? (
          <Loading size="sm" />
        ) : (
          <Reload
            onClick={onRefresh}
            data-test="62711ac7-4ae7-47d2-bf02-0d570c1f4db3"
            className="text-icon-neutral-secondary size-6 cursor-pointer"
          />
        )}
      </div>
    );
  },
);

InputCaptcha.displayName = "InputCaptcha";
