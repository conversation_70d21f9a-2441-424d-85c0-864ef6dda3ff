import { cva } from "class-variance-authority";

export const filePickerVariants = cva(
  "relative flex flex-col items-start justify-center w-full h-full outline outline-dashed rounded transition-colors cursor-pointer py-3 ps-3 ps-2",
  {
    variants: {
      variant: {
        default:
          "outline-border-nautral-default hover:outline-border-primary-default",
        error: "outline-border-error-default",
        success: "outline-border-success-default",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

// Helper function to create a FileList with a single file
export const createFileListWithSingleFile = (file: File): FileList => {
  const dataTransfer = new DataTransfer();
  dataTransfer.items.add(file);
  return dataTransfer.files;
};
