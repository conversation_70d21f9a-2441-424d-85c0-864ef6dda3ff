import * as React from "react";
import { cn } from "@workspace/ui/lib/utils";
import AttachSquare from "@workspace/ui/assets/icons/attach-square.svg";
import Trash from "@workspace/ui/assets/icons/Trash.svg";
import Add from "@/assets/icons/Add.svg";
import { toast } from "@workspace/ui/components/toast";

import { FilePickerProps } from "@workspace/ui/components/file-picker/types";
import {
  createFileListWithSingleFile,
  filePickerVariants,
} from "@/components/file-picker/utils";
import {
  formatFileSize,
  isFileSizeValid,
  isImageFile,
} from "@workspace/ui/lib/file";

const FilePicker = React.forwardRef<HTMLDivElement, FilePickerProps>(
  (
    {
      className,
      rootClassName,
      variant = "default",
      onFilesChange,
      accept,
      multiple = false,
      disabled = false,
      placeholder = "فایل ضمیمه را انخاب کرده یا بکشید و اینجا رها کنید",
      maxSize,
      icon,
      hintText,
      ...props
    },
    ref,
  ) => {
    const [isDragActive, setIsDragActive] = React.useState(false);
    const [selectedFiles, setSelectedFiles] = React.useState<FileList | null>(
      null,
    );
    const [imagePreviews, setImagePreviews] = React.useState<string[]>([]);
    const [error, setError] = React.useState<string | null>(null);
    const inputRef = React.useRef<HTMLInputElement>(null);

    const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      if (!isDisabled) {
        setIsDragActive(true);
      }
    };

    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragActive(false);
    };

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      if (!isDisabled) {
        setIsDragActive(true);
      }
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragActive(false);

      if (isDisabled) return;

      const files = e.dataTransfer.files;
      if (files && files.length > 0) {
        processFiles(files);
      }
    };

    // Helper function to process files and validate them
    const processFiles = (files: FileList) => {
      // If multiple is false, only take the first file
      const processedFiles =
        !multiple && files.length > 1
          ? createFileListWithSingleFile(files.item(0)!)
          : files;

      // Check file sizes
      const invalidFiles: File[] = [];
      const validFiles: File[] = [];

      for (let i = 0; i < processedFiles.length; i++) {
        const file = processedFiles.item(i);
        if (file) {
          if (isFileSizeValid(file, maxSize)) {
            validFiles.push(file);
          } else {
            invalidFiles.push(file);
          }
        }
      }

      if (invalidFiles.length > 0) {
        toast.error(undefined, {
          description: `حجم فایل بیشتر از ${formatFileSize(maxSize!)} است`,
        });
        return;
      }

      setError(null);

      if (validFiles.length > 0) {
        const dataTransfer = new DataTransfer();
        validFiles.forEach((file) => dataTransfer.items.add(file));
        const finalFiles = dataTransfer.files;

        setSelectedFiles(finalFiles);
        onFilesChange(finalFiles);

        // Generate image previews for the valid files
        generateImagePreviews(finalFiles).then((previews) => {
          setImagePreviews(previews);
        });
      }
    };

    // Helper function to generate image previews
    const generateImagePreviews = (files: FileList): Promise<string[]> => {
      const promises: Promise<string>[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files.item(i);
        if (file && isImageFile(file)) {
          promises.push(
            new Promise((resolve) => {
              const reader = new FileReader();
              reader.onload = (e) => resolve(e.target?.result as string);
              reader.readAsDataURL(file);
            }),
          );
        }
      }

      return Promise.all(promises);
    };

    const handleClick = () => {
      if (!isDisabled && inputRef.current) {
        inputRef.current.click();
      }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        processFiles(files);
      } else {
        setSelectedFiles(null);
        setImagePreviews([]);
        setError(null);
        onFilesChange(null);
      }
    };

    const handleClearFiles = (e: React.MouseEvent) => {
      e.stopPropagation();
      setSelectedFiles(null);
      setImagePreviews([]);
      setError(null);
      onFilesChange(null);
      if (inputRef.current) {
        inputRef.current.value = "";
      }
    };

    const hasFiles = selectedFiles && selectedFiles.length > 0;
    const isDisabled = Boolean(disabled) || hasFiles;

    return (
      <div>
        <div
          ref={ref}
          className={cn(
            filePickerVariants({ variant: error ? "error" : variant }),
            isDragActive &&
              "outline-border-primary-default bg-surface-primary-default/25",
            isDisabled && "!outline-border-nautral-disable cursor-not-allowed",
            hasFiles && "!outline-0",
            rootClassName,
            className,
          )}
          onDragEnter={handleDragEnter}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleClick}
          {...props}
        >
          <input
            ref={inputRef}
            type="file"
            accept={accept}
            multiple={multiple}
            onChange={handleFileChange}
            className="hidden"
            disabled={isDisabled || undefined}
          />
          {hasFiles ? (
            <div className="flex w-full flex-col items-center justify-center">
              <div className="max-h-32 w-full overflow-y-auto">
                <ul className="space-y-1">
                  {Array.from(selectedFiles).map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between"
                    >
                      <li className="text-text-nautral-default flex items-center gap-2 text-xs">
                        {/* Show image preview next to file name if it's an image */}
                        {isImageFile(file) && imagePreviews[index] && (
                          <div className="relative">
                            <img
                              src={imagePreviews[index]}
                              alt={`Preview ${index + 1}`}
                              className="border-border-nautral-default size-10 flex-shrink-0 rounded border-2 object-cover"
                            />
                          </div>
                        )}
                        <span className="max-w-52 truncate">
                          {file.name} ({formatFileSize(file.size)})
                        </span>
                      </li>
                      {/* Show trash icon only on the first image preview */}
                      {index === 0 && (
                        <button
                          onClick={handleClearFiles}
                          className="text-icon-neutral-secondary hover:text-text-primary-hover cursor-pointer opacity-100 transition-colors"
                          aria-label="Clear files"
                        >
                          <Trash className="size-6" />
                        </button>
                      )}
                    </div>
                  ))}
                </ul>
              </div>
            </div>
          ) : (
            <div className="flex flex-row items-center justify-center gap-1 text-start">
              {icon || (
                <Add
                  className={cn(
                    "size-4",
                    isDisabled
                      ? "text-icon-neutral-disable"
                      : "text-icon-neutral-secondary",
                  )}
                />
              )}
              <p
                className={cn(
                  "text-xs",
                  isDisabled
                    ? "text-text-nautral-disable"
                    : "text-text-nautral-secondary",
                )}
              >
                {placeholder}
              </p>
            </div>
          )}
        </div>

        {!hasFiles && hintText && (
          <div className="mt-0.5 ps-3 text-start">
            <p className="text-text-nautral-secondary text-[10px]">
              {hintText}
            </p>
          </div>
        )}
      </div>
    );
  },
);

FilePicker.displayName = "FilePicker";

export { FilePicker, filePickerVariants };
