import { FilePicker } from "@/components/file-picker/file-picker";
import type { Meta, StoryObj } from "@storybook/react";
import { useState } from "react";
import { Toaster } from "@workspace/ui/components/toast";

const meta: Meta<typeof FilePicker> = {
  title: "Web/FilePicker",
  component: FilePicker,
  parameters: {
    layout: "centered",
  },
  decorators: [
    (Story) => (
      <div className="container max-w-md p-8">
        <Toaster />
        <Story />
      </div>
    ),
  ],
  argTypes: {
    variant: {
      options: ["default", "error", "success"],
      control: { type: "radio" },
    },
    accept: {
      control: { type: "text" },
    },
    multiple: {
      control: { type: "boolean" },
    },
    disabled: {
      control: { type: "boolean" },
    },
    placeholder: {
      control: { type: "text" },
    },
    maxSize: {
      control: { type: "number" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof FilePicker>;

const FilePickerWithState = (args: any) => {
  const [files, setFiles] = useState<FileList | null>(null);

  const handleFilesChange = (selectedFiles: FileList | null) => {
    setFiles(selectedFiles);
  };

  return (
    <div className="space-y-4">
      <FilePicker {...args} onFilesChange={handleFilesChange} />
    </div>
  );
};

export const Default: Story = {
  args: {
    placeholder: "تصویر فیش واریزی",
    hintText: "حداکثر حجم فایل بارگذاری : 1 مگابایت",
    accept: ".jpg,.png",
    maxSize: 1000000,
  },
  render: (args) => <FilePickerWithState {...args} />,
};
