import React from "react";
import Growt from "@/assets/icons/Growt.svg";
import GrowthBold from "@/assets/icons/Growth-bold.svg";

function Funds({ isSelected = false }) {
  return (
    <div>
      {isSelected ? (
        <div className="flex flex-col items-center justify-center gap-1">
          <GrowthBold className="text-icon-primary-default size-6" />
          <div className="text-text-primary-default text-[10px] font-normal">
            صندوق‌ها
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center gap-1">
          <Growt className="text-text-nautral-secondary size-6" />
          <div className="text-text-nautral-secondary text-[10px] font-normal">
            صندوق‌ها
          </div>
        </div>
      )}
    </div>
  );
}

export default Funds;
