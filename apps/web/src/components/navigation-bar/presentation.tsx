import Graph from "@/assets/icons/graph.svg";
import Funds from "@/components/navigation-bar/menu-items/funds/Funds";
import Home from "@/components/navigation-bar/menu-items/home/<USER>";
import { ROUTE_FUNDS, ROUTE_HOME } from "@/constants/routes";
import Link from "next/link";
import { usePathname } from "next/navigation";
import styles from "./navigation.module.scss";
import { cn } from "@workspace/ui/lib/utils";
function NavigationBar() {
  const path = usePathname();

  return (
    <>
      <div
        className={cn(
          "fixed right-0 bottom-0 left-0 z-50 mx-auto max-w-2xl",
          styles.root,
        )}
      >
        <div
          className={cn(
            "roundsed-t-2xl backdropd-blur-xs h-full w-full px-2 pb-2",
            styles.inner,
          )}
          style={{}}
        >
          <div className="bg-surface-nautral-default-4 shadow-navigation flex w-full items-center justify-around rounded-2xl px-2 py-3">
            <Link href={ROUTE_FUNDS} className="px-[6.5px] py-1.5">
              <Funds isSelected={path === "/funds"} />
            </Link>

            <Link href={ROUTE_HOME}>
              <Home isSelected={path === "/"} />
            </Link>

            <div className="flex flex-col items-center justify-center gap-1 px-[6.5px] py-1.5">
              <Graph className="text-text-nautral-secondary size-6" />
              <div className="text-text-nautral-secondary text-[10px] font-normal">
                دارایی من
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        style={{ height: 95, width: "100%", display: "block", flexShrink: 0 }}
      />
    </>
  );
}

export default NavigationBar;
