"use client";

import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { useMemo } from "react";
import getChartOption from "./utils";

function PieChart({ data }: { data: number[] }) {
  const options = useMemo(() => ({ ...getChartOption(data) }), data);

  return (
    <HighchartsReact
      immutable
      containerProps={{
        className:
          "flex items-center justify-center h-full w-full min-h-[114px] min-w-[114px] -ml-2",
      }}
      highcharts={Highcharts}
      options={options}
    />
  );
}

export default PieChart;
