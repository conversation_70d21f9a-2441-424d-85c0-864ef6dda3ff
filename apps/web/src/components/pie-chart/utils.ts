import * as Highcharts from "highcharts";

const getChartOption = (data: number[]): Highcharts.Options => {
  return {
    tooltip: { enabled: false },
    chart: {
      backgroundColor: "transparent",
      plotBackgroundColor: "transparent",
      plotBorderWidth: 0,
      plotShadow: false,
      type: "pie",
      animation: false,
      margin: 0,
      spacingTop: 0,
      spacingBottom: 0,
      spacingLeft: 0,
      spacingRight: 0,
    },
    plotOptions: {
      pie: {
        allowPointSelect: false,
        cursor: "pointer",
        dataLabels: {
          enabled: false,
        },
        showInLegend: false,
        borderWidth: 0,
        animation: false,
      },
    },
    title: { text: "" },
    subtitle: { text: "" },
    credits: { enabled: false },
    series: [
      {
        name: "",
        type: "pie",
        borderRadius: 0,
        states: {
          hover: {
            enabled: false,
          },
          inactive: {
            opacity: 1,
          },
        },
        data,
      },
    ],
  };
};

export default getChartOption;
