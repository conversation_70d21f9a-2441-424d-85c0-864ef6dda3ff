import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import <PERSON><PERSON><PERSON> from "@/components/pie-chart";

const meta: Meta<typeof PieChart> = {
  title: "Web/pieChart",
  component: <PERSON><PERSON><PERSON>,
  parameters: {
    layout: "centered",
  },
  // tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div
        style={{ height: 230, padding: 36 }}
        className="bg-surface-nautral-transparent container max-w-sm"
        dir="rtl"
      >
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof PieChart>;

export const Chart: Story = {
  args: {
    data: [5, 20.33, 77.66, 11.33, 70.66],
  },
};
