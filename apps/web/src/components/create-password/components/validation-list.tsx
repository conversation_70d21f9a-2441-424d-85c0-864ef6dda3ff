import React from "react";
import { cn } from "@workspace/ui/lib/utils";
import Check from "@/assets/icons/Check.svg";
import { IValidationList } from "./types";

const ValidationList = ({ checkItems, isBlur, password }: IValidationList) => {
  return (
    <div className="pt-2 pr-0.5 pb-4">
      {checkItems?.map((item) => {
        const isValid = item.validationFn(password!);
        return (
          <p
            key={item?.key}
            className={cn(
              "text-text-nautral-secondary pb-px text-[10px]",
              isValid && "text-text-success-default",
              isBlur && !isValid && "text-text-error-default-2",
            )}
          >
            {isValid ? (
              <Check className="inline w-4" />
            ) : (
              <span
                className={cn(
                  "bg-icon-neutral-secondary mr-1 ml-2.5 inline-block h-1 w-1 rounded-full",
                  isBlur && !isValid && "bg-text-error-default-2",
                )}
              />
            )}

            {item?.label}
          </p>
        );
      })}
    </div>
  );
};
export default ValidationList;
