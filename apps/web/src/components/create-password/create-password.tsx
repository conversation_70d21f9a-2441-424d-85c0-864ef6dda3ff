import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@workspace/ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { InputPassword } from "@workspace/ui/components/input-password";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { ICreatePassword } from "./types";
import { checkItems, formSchema } from "./utils";
import { useState } from "react";
import ValidationList from "@/components/create-password/components/validation-list";
import { Loading } from "@workspace/ui/components/loading";

const CreatePassword = ({ onSubmit }: ICreatePassword) => {
  const [isBlur, setIsBlur] = useState(false);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });
  const password = form.watch("password");
  const hasError =
    checkItems.find((item) => !item?.validationFn(password)) &&
    form?.formState?.isDirty;

  function onFormSubmit(values: z.infer<typeof formSchema>) {
    if (!hasError) {
      return onSubmit({
        password: values.password,
        confirmPassword: values.confirmPassword,
      });
    }
  }

  return (
    <>
      <DialogHeader>
        <DialogTitle className="text-text-nautral-default border-b-surface-nautral-modal-cover border-b pt-[26px] pr-4 pb-[11px] text-xs">
          ایجاد رمز عبور
        </DialogTitle>
      </DialogHeader>
      <div className="flex flex-col justify-between px-4 pb-4">
        <div className="flex flex-col justify-between gap-6">
          <Form {...form}>
            <form
              id="password-form"
              onSubmit={form.handleSubmit(onFormSubmit)}
              className="flex flex-col justify-between"
            >
              <div className="flex flex-col justify-between pt-5 pb-[55px]">
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormControl>
                        <InputPassword
                          size="lg"
                          title="رمز عبور جدید"
                          {...field}
                          onBlur={() => {
                            field?.onBlur && field.onBlur();
                            setIsBlur(true);
                          }}
                          onFocus={() => {
                            setIsBlur(false);
                          }}
                          helperText={fieldState?.error?.message}
                          aria-invalid={
                            (hasError && isBlur) || !!fieldState?.error?.message
                          }
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <ValidationList {...{ password, checkItems, isBlur }} />
                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormControl>
                        <InputPassword
                          size="lg"
                          title="تکرار رمز عبور جدید"
                          {...field}
                          helperText={fieldState?.error?.message}
                          aria-invalid={!!fieldState?.error?.message}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </form>
          </Form>
        </div>
        <Button
          disabled={form.formState.isSubmitting}
          endAdornment={form.formState.isSubmitting && <Loading size="sm" />}
          variant="fill"
          size="md"
          form="password-form"
          type="submit"
        >
          تایید و ادامه
        </Button>
      </div>
    </>
  );
};

export default CreatePassword;
