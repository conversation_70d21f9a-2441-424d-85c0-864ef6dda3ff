import { z } from "zod";

export const checkItems = [
  {
    label: "حروف انگلیسی کوچک و بزرگ",
    key: "upper",
    validationFn: (value: string) =>
      /[A-Z]/.test(value) &&
      /[a-z]/.test(value) &&
      !/[\u0600-\u06FF]/.test(value),
  },
  {
    label: "حداقل یک کاراکتر از اعداد",
    key: "number",
    validationFn: (value: string) => /\d/.test(value),
  },
  {
    label: "کاراکتر‌های ویژه(@،&،# و...)",
    key: "signs",
    validationFn: (value: string) => /(?=.*[!@#$%^&*])/.test(value),
  },
  {
    label: "حداقل 12 کاراکتر",
    key: "charLength",
    validationFn: (value: string) => value?.length >= 12,
  },
];

export const formSchema = z
  .object({
    password: z.string().nonempty("رمز عبور را وارد کنید."),
    confirmPassword: z.string().nonempty("تکرار رمز عبور را وارد کنید."),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "رمزعبور با تکرار آن برابر نیست!",
    path: ["confirmPassword"],
  });
