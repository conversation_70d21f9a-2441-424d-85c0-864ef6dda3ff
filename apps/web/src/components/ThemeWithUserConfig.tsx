"use client";

import { ReactNode } from "react";
import useUserConfig from "@/hooks/useUserConfig";
import { ThemeProvider as NextThemesProvider, useTheme } from "next-themes";

function ThemeWithUserConfig({ children }: { children: ReactNode }) {
  const { configs } = useUserConfig();

  // If darkMode is "system", do not force theme, let next-themes detect system theme
  const forcedTheme =
    configs.darkMode === "dark" || configs.darkMode === "light"
      ? configs.darkMode
      : undefined;

  return (
    <NextThemesProvider
      attribute="class"
      forcedTheme={forcedTheme}
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
      enableColorScheme
    >
      {children}
    </NextThemesProvider>
  );
}

export default ThemeWithUserConfig;
