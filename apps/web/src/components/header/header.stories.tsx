import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Header from "./presentation";

const meta: Meta<typeof Header> = {
  title: "Web/header",
  component: Header,
  parameters: {
    layout: "centered",
  },
  // tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="container max-w-sm p-8" dir="rtl">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof Header>;

export const Primary: Story = {
  args: {
    title: "Header",
    subTitle: "desc",
    center: false,
  },
};
