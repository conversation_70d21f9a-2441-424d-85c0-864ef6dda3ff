import User from "@/assets/icons/User.svg";
import Question from "@/assets/icons/question.svg";
import { cn } from "@workspace/ui/lib/utils";
import { ReactNode } from "react";

function Header({
  title,
  subTitle,
  startAdornment,
  endAdornment,
  center = false,
  titleClassName,
  subTitleClassName,
}: {
  title?: string;
  subTitle?: ReactNode;
  startAdornment?: ReactNode | null;
  endAdornment?: ReactNode | null;
  center?: boolean;
  titleClassName?: string;
  subTitleClassName?: string;
}) {
  return (
    <div className="bg-surface-nautral-default-4 flex items-center justify-between gap-2 p-4">
      {startAdornment || (
        <div className="bg-surface-nautral-default-3 shrink-0 rounded-sm p-2">
          <User className="text-icon-primary-default size-4" />
        </div>
      )}

      <div
        className={cn(
          "flex flex-1 flex-col",
          center ? "items-center" : "items-start",
        )}
      >
        <div
          className={cn(
            "text-text-nautral-default text-[10px] font-bold",
            titleClassName,
          )}
        >
          {title}
        </div>

        <div
          className={cn(
            "text-text-warning-default text-[10px] font-normal",
            subTitleClassName,
          )}
        >
          {subTitle}
        </div>
      </div>

      {endAdornment || (
        <div className="bg-surface-nautral-default-3 shrink-0 rounded-sm p-2">
          <Question className="text-icon-primary-default size-4" />
        </div>
      )}
    </div>
  );
}

export default Header;
