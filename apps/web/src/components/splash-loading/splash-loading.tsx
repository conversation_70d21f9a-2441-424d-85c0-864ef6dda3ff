import <PERSON>tie from "lottie-react";
import <PERSON><PERSON><PERSON><PERSON> from "@/assets/icons/splash-logo.svg";
import animationData from "@/assets/animations/splash-loading.json";

function SplashLoading() {
  return (
    <div className="bg-surface-nautral-default-4 absolute top-0 right-0 bottom-0 left-0 z-50 flex h-full flex-col items-center justify-between p-7">
      <div />
      <div className="mt-[155px] flex flex-1 flex-col items-center gap-2">
        <Lottie
          animationData={animationData}
          loop={true}
          style={{ width: 145, height: 78 }}
        />
        <SabaLogo className="text-icon-neutral-default h-[18px] w-[170px]" />
      </div>
      <p className="text-text-nautral-secondary text-[10px] font-normal">
        Version 0.1.2
      </p>
    </div>
  );
}

export default SplashLoading;
