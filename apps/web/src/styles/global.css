@import "../../../../packages/ui/src/styles/globals.css";
@import "./variables.css";
@import "swiper/css";
@import "swiper/css/navigation";
@import "swiper/css/pagination";
@import "./tailwind-theme.css";

html {
  touch-action: pan-y;
}

body {
  font-family: var(--font-yekan) !important;
}

@theme inline {
  --shadow-navigation: 0px 0px 4px 0px #00000040;
}

.grecaptcha-badge {
  @apply hidden;
}

@theme inline {
  --color-icon-on-surface-primary: var(--icon-on-surface-primary);
  --color-text-on-surface-primary: var(--text-on-surface-primary);
}

[data-sonner-toaster] {
  font-family: var(--font-yekan) !important;
}

.dark {
  .profile-bg-Pattern {
    background: repeating-linear-gradient(
      -35deg,
      #807f7f1c 0px,
      #3c3c3f 1.5px,
      #343438 0px,
      #343438 3px
    );
  }
}

.profile-bg-Pattern {
  background: repeating-linear-gradient(
    -35deg,
    #807f7f1c 0px,
    #efefef 0.5px,
    #fcfcfc 0px,
    #fcfcfc 3px
  );
}

div[role="dialog"] {
  max-height: 100vh !important;
  height: auto !important;
}

.dark {
  .bg-zebra {
    background: repeating-linear-gradient(
      -30deg,
      #807f7f1c 0px,
      #2e2e32 0.5px,
      #252529 0px,
      #252529 4px
    );
  }
}

.bg-zebra {
  background: repeating-linear-gradient(
    -30deg,
    #807f7f1c 0px,
    #f0f0f0 0.5px,
    #fcfcfc 0px,
    #fcfcfc 3px
  );
}
