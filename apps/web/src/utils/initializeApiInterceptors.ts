import { apiService } from "@workspace/investment-api/apiService";
import { AxiosError, AxiosResponse } from "axios";

import { ROUTE_LOGIN } from "@/constants/routes";
import { removeAllCookies } from "@/hooks/useCookie";

const notAllowedStatusCodes = [401];

export const initializeApiInterceptors = () => {
  apiService.interceptors.response.use(
    (response: AxiosResponse) => {
      if (!response?.data?.isSuccess)
        throw new Error(response?.data?.errorMessage || "server has error");

      return response;
    },
    async (error: AxiosError) => {
      try {
        const { config, status } = error;

        if (
          config &&
          status &&
          notAllowedStatusCodes.includes(status) &&
          typeof window !== "undefined" &&
          ![ROUTE_LOGIN].includes(window.location.pathname)
        ) {
          removeAllCookies();
        }

        return Promise.reject(error);
      } catch (err) {
        return Promise.reject(err);
      }
    },
  );
};
