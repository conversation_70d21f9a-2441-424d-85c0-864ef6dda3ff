import { format, sub } from "date-fns";

type CalendarType = "gregory" | "persian" | string;
type TCalendarContext = {
  format(fmt: string): string;
  locale(loc: string): TCalendarContext;
  calendar(cal: CalendarType): TCalendarContext;
  toString(): string;
};

/**
 * Convert date to any format
 * @param formats
 * `YYYY`: 2024  `YY`: 24 | `MMMM`: January | `MMM`: Jan | `MM`: 01 | `M`: 1 | `DD`: 01 | `D`: 1 | `HH`: 00-23 | 
 * `H`: 0-23 | `hh`: 01-12 | `h`: 1-12 | `mm`: 00-59 | `m`: 0-59 | `ss`: 00-59 | `s`: 0-59 | `A`: AM/PM | `a`: am/pm | `dddd`: Sunday | `ddd`: Sun | `dd`: S
 
 * @default format "YYYY/MM/DD"
 * @default locale "en-US"
 * @default calendar "persian"
 *
 * @example dateConverter("2024-02-06T20:00:00.000Z").toString() // 1402/11/17
 * @example dateConverter(new Date("2024-02-06T20:00:00.000Z")).toString() // 1402/11/17
 * @example dateConverter("2024-02-06T20:00:00.000Z").format("YYYY/MM/DD")
 * @example dateConverter("2024-02-06T20:00:00.000Z").locale("ar-EG").calendar("persian").format("YYYY/MM/DD")
 */
export function dateConverter(date: Date | string | number): TCalendarContext {
  let format = "YYYY/MM/DD";
  let locale = "en-US";
  let calendar: CalendarType = "persian";

  const getFormattedDate = () => {
    if (!date) {
      return "";
    }
    const finalDate =
      typeof date === "string" || typeof date === "number"
        ? new Date(date)
        : date;

    const yearFull = Intl.DateTimeFormat(locale, { year: "numeric", calendar })
      .format(finalDate)
      .replace(/\s.*$/, "");
    const year2Digit = Intl.DateTimeFormat(locale, {
      year: "2-digit",
      calendar,
    })
      .format(finalDate)
      .replace(/\s.*$/, "");
    const monthLong = Intl.DateTimeFormat(locale, {
      month: "long",
      calendar,
    }).format(finalDate);
    const monthNumeric = Intl.DateTimeFormat(locale, {
      month: "numeric",
      calendar,
    }).format(finalDate);
    const monthShort = Intl.DateTimeFormat(locale, {
      month: "short",
      calendar,
    }).format(finalDate);
    const month2Digit = Intl.DateTimeFormat(locale, {
      month: "2-digit",
      calendar,
    }).format(finalDate);
    const dayFull = Intl.DateTimeFormat(locale, {
      day: "2-digit",
      calendar,
    }).format(finalDate);
    const dayShort = Intl.DateTimeFormat(locale, {
      day: "numeric",
      calendar,
    }).format(finalDate);
    const weekdayFull = Intl.DateTimeFormat(locale, {
      weekday: "long",
      calendar,
    }).format(finalDate);
    const weekdayShort = Intl.DateTimeFormat(locale, {
      weekday: "short",
      calendar,
    }).format(finalDate);
    const weekdayNarrow = Intl.DateTimeFormat(locale, {
      weekday: "narrow",
      calendar,
    }).format(finalDate);
    let hour24Full = Intl.DateTimeFormat(locale, {
      hour: "2-digit",
      hour12: false,
      calendar,
    }).format(finalDate);
    if (hour24Full.startsWith("0")) {
      hour24Full = hour24Full?.replace(/^0(\d)$/, "$1");
    }

    const hour24Short = Intl.DateTimeFormat(locale, {
      hour: "numeric",
      hour12: false,
      calendar,
    }).format(finalDate);
    const hour12Full = Intl.DateTimeFormat(locale, {
      hour: "2-digit",
      hour12: true,
      calendar,
    }).format(finalDate);
    const hour12Short = Intl.DateTimeFormat(locale, {
      hour: "numeric",
      hour12: true,
      calendar,
    }).format(finalDate);
    const minuteFull = Intl.DateTimeFormat(locale, {
      minute: "2-digit",
      calendar,
    }).format(finalDate);
    const minuteShort = Intl.DateTimeFormat(locale, {
      minute: "numeric",
      calendar,
    }).format(finalDate);
    const secondFull = Intl.DateTimeFormat(locale, {
      second: "2-digit",
      calendar,
    }).format(finalDate);
    const secondShort = Intl.DateTimeFormat(locale, {
      second: "numeric",
      calendar,
    }).format(finalDate);
    // replace all numbers and just letters
    const ampm = hour12Full.replace(/[0-9\s]/g, "");

    // Process tokens from longest to shortest to avoid conflicts
    const replacements = [
      { token: "YYYY", value: yearFull },
      { token: "YY", value: year2Digit },
      { token: "MMMM", value: monthLong },
      { token: "MMM", value: monthShort },
      { token: "MM", value: month2Digit },
      { token: "M", value: monthNumeric },
      { token: "dddd", value: weekdayFull },
      { token: "ddd", value: weekdayShort },
      { token: "dd", value: weekdayNarrow },
      { token: "DD", value: dayFull },
      { token: "D", value: dayShort },
      { token: "HH", value: hour24Full },
      { token: "H", value: hour24Short },
      { token: "hh", value: hour12Full.replace(/\s.*$/, "") },
      { token: "h", value: hour12Short.replace(/\s.*$/, "") },
      { token: "mm", value: minuteFull },
      { token: "m", value: minuteShort },
      { token: "ss", value: secondFull },
      { token: "s", value: secondShort },
      { token: "A", value: ampm },
      { token: "a", value: ampm?.toLowerCase() },
    ];

    let result = format;
    const tokenMap = new Map();

    // Sort replacements by token length (longest first) to avoid conflicts
    const sortedReplacements = [...replacements].sort(
      (a, b) => b.token.length - a.token.length,
    );

    // Create a temporary placeholder for each token
    const PLACEHOLDER_PREFIX = "@@TOKEN_";
    const PLACEHOLDER_SUFFIX = "@@";

    // First pass: Replace tokens with numbered placeholders
    sortedReplacements.forEach(({ token, value }, index) => {
      const placeholder = `${PLACEHOLDER_PREFIX}${index}${PLACEHOLDER_SUFFIX}`;
      result = result.replace(new RegExp(token, "g"), placeholder);
      tokenMap.set(placeholder, value);
    });

    // Second pass: Replace numbered placeholders with values
    tokenMap.forEach((value, placeholder) => {
      result = result.replace(new RegExp(placeholder, "g"), value);
    });

    return result;
  };

  const context: TCalendarContext = {
    format(fmt: string) {
      format = fmt;
      return getFormattedDate();
    },
    locale(loc: string) {
      locale = loc;
      return this;
    },
    calendar(cal: CalendarType) {
      calendar = cal;
      return this;
    },
    toString() {
      return getFormattedDate();
    },
  };

  return context;
}

export const subtractMonthFromDate = (value: number, date?: string) =>
  sub(
    date ? new Date(date) : new Date(),
    {
      months: value,
    },
    {},
  );
