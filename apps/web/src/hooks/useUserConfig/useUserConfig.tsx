import { USER_CONFIGS } from "@/constants/localStorage";
import { defaultConfig } from "@/hooks/useUserConfig/constants";
import { UserConfigData } from "@/hooks/useUserConfig/types";
import { parseConfig } from "@/hooks/useUserConfig/utils";
import { IsLoggedIn } from "@/utils/auth";
import { getLocalStorage, setLocalStorage } from "@/utils/localStorage";
import {
  useUserFrontendConfigGetUserConfig,
  useUserFrontendConfigEditUserConfig,
} from "@workspace/investment-api/auto-generated/apis/user-frontend-config/user-frontend-config";
import { useEffect, useState } from "react";

const useUserConfig = <K extends keyof UserConfigData>(): {
  setConfig: (key: K, value: UserConfigData[K]) => Promise<void>;
  configs: UserConfigData;
  isLoading: boolean;
} => {
  const isLoggedIn = IsLoggedIn();

  const {
    data: userConfigData,
    refetch,
    isLoading,
  } = useUserFrontendConfigGetUserConfig({ query: { enabled: !!isLoggedIn } });

  const { mutateAsync: editUserConfigAsync } =
    useUserFrontendConfigEditUserConfig();

  // Lazy initializer, safe for SSR using isClient()
  const getInitialConfig = (): UserConfigData => {
    const localConfigString = getLocalStorage(USER_CONFIGS);
    const localConfigPartial = parseConfig(localConfigString);
    if (localConfigPartial) {
      return { ...defaultConfig, ...localConfigPartial };
    }

    return defaultConfig;
  };

  const [configs, setConfigs] = useState<UserConfigData>(getInitialConfig());

  // Sync configs from localStorage when login state changes
  useEffect(() => {
    setConfigs(getInitialConfig());
  }, [isLoggedIn]);

  // Update configs from API data, safe localStorage access
  useEffect(() => {
    if (!userConfigData?.data || !isLoggedIn) return;

    const apiContentString = userConfigData.data || null;
    const apiConfigPartial = parseConfig(apiContentString);

    if (apiConfigPartial) {
      const merged = { ...defaultConfig, ...apiConfigPartial };
      setConfigs(merged);
      setLocalStorage(USER_CONFIGS, JSON.stringify(apiConfigPartial));
    } else {
      const localConfigString = getLocalStorage(USER_CONFIGS);
      const localConfigPartial = parseConfig(localConfigString);
      if (localConfigPartial) {
        setConfigs({ ...defaultConfig, ...localConfigPartial });
      } else {
        setConfigs(defaultConfig);
        setLocalStorage(USER_CONFIGS, JSON.stringify({}));
      }
    }
  }, [userConfigData, isLoggedIn]);

  // Update one userConfig key and sync all storage & API

  const setConfig = async <K extends keyof UserConfigData>(
    key: K,
    value: UserConfigData[K],
  ) => {
    const newPartialConfig = {
      ...configs,
      [key]: value,
    };

    const contentString = JSON.stringify(newPartialConfig);

    try {
      await editUserConfigAsync(
        { data: { content: contentString } },
        {
          onSuccess: () => {
            setConfigs(newPartialConfig);
            setLocalStorage(USER_CONFIGS, contentString);

            refetch();
          },
        },
      );
    } catch (error) {
      console.error("خطا:", error);
    }
  };

  return {
    setConfig,
    configs,
    isLoading: !!isLoading,
  };
};

export default useUserConfig;
