import { useAuthLogout } from "@workspace/investment-api/auto-generated/apis/auth/auth";
import { useRouter } from "next/navigation";
import { ROUTE_LOGIN } from "@/constants/routes";
import { removeAllCookies } from "@/hooks/useCookie";
import { clearLocalstorage } from "@/utils/clearLocalstorage";

export const useLogout = () => {
  const router = useRouter();
  const { data, isLoading, isError, refetch } = useAuthLogout({
    query: {
      enabled: false,
      retry: 0,
    },
  });

  const handleLogout = () => {
    refetch().then(() => {
      removeAllCookies();
      clearLocalstorage();

      router.replace(ROUTE_LOGIN);
      window.location.reload();
    });
  };

  return {
    logout: handleLogout,
    isLoading,
    isError,
    data,
  };
};
