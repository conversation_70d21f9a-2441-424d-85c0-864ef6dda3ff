import { COOKIE_IS_LOGGED_IN } from "@/constants/cookies";
import { ROUTE_HOME } from "@/constants/routes";
import { setCookie } from "@/hooks/useCookie";
import { useRouter } from "next/navigation";

export const useLogin = () => {
  const router = useRouter();

  const handleLogin = () => {
    setCookie(COOKIE_IS_LOGGED_IN, "true");

    router.replace(ROUTE_HOME);
    window.location.reload();
  };

  return {
    login: handleLogin,
  };
};
