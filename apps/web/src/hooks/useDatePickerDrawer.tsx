"use client";
import { useResponsiveModal } from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import MobilePicker from "@workspace/ui/components/mobile-picker/mobile-picker";
import { DialogTitle } from "@workspace/ui/components/dialog";

const useDatePickerDrawer = () => {
  const { openModal, closeModal } = useResponsiveModal();

  const id = `date-modal-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  function onCloseModal() {
    closeModal(id);
  }

  const openDatePickerModal = (
    onDateChange: (date: Date) => void,
    selectedDate?: Date,
  ) => {
    return openModal({
      id,
      content: (
        <>
          <DialogTitle />
          <MobilePicker value={selectedDate} onChange={onDateChange} />
        </>
      ),
    });
  };

  return { openDatePickerModal, closeModal: onCloseModal };
};

export default useDatePickerDrawer;
