"use client";
import { useResponsiveModal } from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import { DialogHeader, DialogTitle } from "@workspace/ui/components/dialog";

const useRulesDrawer = () => {
  const { openModal } = useResponsiveModal();

  const renderRules = () => (
    <div className="p-4 text-xs">
      <DialogHeader>
        <DialogTitle className="border-b-surface-nautral-modal-cover mb-7 border-b pt-6 pb-1 text-xs">
          قوانین و مقررات
        </DialogTitle>
      </DialogHeader>
      <div className="text-text-nautral-secondary max-h-[80vh] grow space-y-4 overflow-auto pb-3 leading-5">
        <p>
          صدور واحدهای سرمایه گذاری دو روز کاری بعد از واریز وجه و پرداخت مبلغ،
          قابل مشاهده است.
        </p>
        <p>
          قیمت صدور بر مبنای قیمت NAV روز کاری بعد از ثبت درخواست صدور، می باشد.
        </p>
        <p>
          قیمت خریدی که در زمان پرداخت مشاهده می نمایید با توجه به الزامات
          موجود، مقداری بالاتر از قیمت صدور روز جاری است که پس از صدور نهایی
          واحدهای درخواستی، مبلغ باقیمانده با توجه به قیمت صدور در روز صادر شدن
          واحدها، بعد از دو روز کاری به حساب شما عودت داده خواهد شد.
        </p>
        <p>
          در صورتیکه درخواست ابطال قبل از ساعت 11 صبح روز کاری(شنبه تا چهارشنبه
          به غیر از تعطیلات رسمی و روزهای پنجشنبه و جمعه) ثبت گردد، ارزش اسمی
          همان روز و تتمه ابطال (مابه التفاوت با ارزش اسمی) نیز یک روز کاری بعد
          به حساب سرمایه گذاران واریز میگردد. در غیر این صورت ارزش اسمی ابطال
          های بعد از ساعت مقرر، روز کاری بعد و تتمه ابطال نیز دو روز کاری پس از
          پرداخت ارزش اسمی به حساب شماه واریز می شود.
        </p>
      </div>
    </div>
  );

  const openRulesModal = () => {
    return openModal({
      id: `pre-sejam-modal-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      content: renderRules(),
    });
  };

  return { openRulesModal };
};

export default useRulesDrawer;
