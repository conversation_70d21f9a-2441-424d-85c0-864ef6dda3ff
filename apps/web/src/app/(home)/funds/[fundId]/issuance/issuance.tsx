"use client";

import formatNumberWithCommas from "@workspace/ui/lib/commaSeparator";
import { useEffect, useState } from "react";
import { dateConverter } from "@/utils/dateConverter";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { InputNumber } from "@workspace/ui/components/input-number";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@workspace/ui/components/button";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { Info } from "lucide-react";
import { IIssuance } from "./types";
import useRulesDrawer from "./hooks/useRulesDrawer";
import usePreConfirmDrawer from "@/components/payment-via-bank-receipt/hooks/usePreConfirmationDrawer";

const Issuance = ({
  issuancePrice,
  minIssuancePrice,
  updateTime,
  frequentPrices,
}: IIssuance) => {
  const { openRulesModal } = useRulesDrawer();
  const { openPreConfirmModal } = usePreConfirmDrawer();
  const [isReadRule, setIsReadRule] = useState(false);
  const [unitCount, setUnitCount] = useState(0);
  const formSchema = z.object({
    price: z
      .string()
      .nonempty("مبلغ نمیتواند خالی باشد.")
      .refine(
        (value) => {
          return Number(value) > Number(minIssuancePrice);
        },
        {
          message: `حداقل مبلغ سرمایه‌گذاری ${formatNumberWithCommas(minIssuancePrice as string)} ریال می‌باشد`,
        },
      ),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      price: "",
    },
    mode: "onBlur",
  });
  const price = form.watch("price");

  const findUnitCount = (value: string) => {
    setUnitCount(Math.floor(Number(value) / Number(issuancePrice)));
  };

  function onSubmit(values: z.infer<typeof formSchema>) {
    findUnitCount(values?.price);
  }

  useEffect(() => {
    findUnitCount(price);
  }, [price]);

  const handleFrequentClick = (item: string) => {
    form?.setValue("price", item);
    form?.clearErrors();
  };

  function handleConfirm() {}

  return (
    <div className="flex h-full flex-col justify-between p-4">
      <div className="flex flex-col gap-4">
        <div className="border-surface-nautral-modal-cover mb-2 flex justify-between rounded-lg border px-3 py-2">
          <div className="text-[10px]">
            <div className="text-text-nautral-default">قیمت صدور هر واحد</div>
            <div className="text-text-nautral-secondary pt-1">
              بروز شده در
              {dateConverter(updateTime as number)
                .locale("fa-IR")
                .format("DD MMMM YYYY")}
            </div>
          </div>
          <div className="text-text-nautral-default pt-[7px] text-sm font-bold">
            {issuancePrice ? formatNumberWithCommas(issuancePrice) : "-"}{" "}
            <span className="text-text-nautral-secondary text-[10px] font-normal">
              ریال
            </span>
          </div>
        </div>
        <Form {...form}>
          <form
            id="issuance-form"
            onSubmit={form.handleSubmit(onSubmit)}
            className=""
          >
            <FormField
              control={form.control}
              name="price"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormControl>
                    <InputNumber
                      size="lg"
                      commaSeparated
                      title="مبلغ سرمایه‌گذاری(ریال)"
                      {...field}
                      onBlur={() => {
                        field.onBlur();
                        form.clearErrors("price");
                      }}
                      autoFocus
                      helperText={fieldState?.error?.message}
                      aria-invalid={!!fieldState?.error?.message}
                    />
                  </FormControl>
                  {!fieldState?.invalid && (
                    <span className="relative bottom-1 pr-3 text-[10px]">
                      حداقل مبلغ سرمایه‌گذاری 1.100.000 ریال می‌باشد
                    </span>
                  )}
                </FormItem>
              )}
            />
          </form>
        </Form>
        <div className="-mt-px mb-2">
          <div className="text-text-nautral-default relative bottom-0.5 pr-px pb-[3px] text-xs">
            مبالغ پرتکرار
          </div>
          <div className="flex w-full justify-around gap-x-0.5">
            {frequentPrices?.map((item) => (
              <div
                key={item}
                onClick={() => handleFrequentClick(item)}
                className="text-text-nautral-secondary bg-surface-nautral-default-1 min-w-12 cursor-pointer rounded-3xl px-2.5 py-2 text-[12px]"
              >
                {formatNumberWithCommas(item)} ریال{" "}
              </div>
            ))}
          </div>
        </div>
        <div className="bg-surface-nautral-default-4 flex items-center justify-between rounded-sm px-2.5 py-2.5">
          <div className="text-text-nautral-secondary text-xs">
            تعداد واحد معادل
          </div>
          <div className="text-text-nautral-secondary text-[10px]">
            <span className="text-text-nautral-default pl-0.5 text-sm font-bold">
              {unitCount}
            </span>
            واحد
          </div>
        </div>
        <div className="text-text-nautral-secondary flex justify-center pb-8 text-[10px]">
          <Info className="ml-1 h-3 w-3" />
          متن اساسنامه و امیدنامه
        </div>
      </div>

      <Checkbox
        className="mb-4"
        onClick={() => setIsReadRule((prev) => !prev)}
        label={
          <div className="text-text-nautral-default pt-0.5 text-xs leading-5">
            متن{" "}
            <span
              onClick={openRulesModal}
              className="text-text-primary-default px-px underline"
            >
              قوانین و مقررات
            </span>
            ، خرید و فروش صندوق را مطالعه کرده و می‌پذیرم
          </div>
        }
      />
      <Button
        variant="fill"
        size="md"
        className="justify-self-end"
        color="success"
        type="submit"
        form="issuance-form"
        disabled={!isReadRule}
        onClick={() => openPreConfirmModal(handleConfirm)}
      >
        ارسال درخواست صدور
      </Button>
    </div>
  );
};

export default Issuance;
