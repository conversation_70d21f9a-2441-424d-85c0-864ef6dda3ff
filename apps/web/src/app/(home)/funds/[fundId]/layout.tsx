"use client";

import MobileLayout from "@/app/(home)/funds/[fundId]/components/layout/mobileLayout";
import { ResponsiveRenderer } from "@workspace/ui/components/responsive-renderer";

interface ILayoutProps {
  children: React.ReactNode;
  params: any;
}

export default function Layout({ children, params }: ILayoutProps) {
  return (
    <ResponsiveRenderer
      desktop={<MobileLayout params={params}>{children}</MobileLayout>}
      mobile={<MobileLayout params={params}>{children}</MobileLayout>}
    />
  );
}
