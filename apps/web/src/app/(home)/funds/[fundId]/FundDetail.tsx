import FundCard from "@/app/(home)/funds/[fundId]/components/FundCard";
import IssuanceCard from "@/app/(home)/funds/[fundId]/components/IssuanceCard";
import RiskLevel from "@/app/(home)/funds/[fundId]/components/RiskLevel";
import { useFinancialInstitutesGetAll } from "@workspace/investment-api/auto-generated/apis/financial-institutes/financial-institutes";
import { useProfileGetLoggedInUserProfile } from "@workspace/investment-api/auto-generated/apis/profile/profile";
import React, { useMemo, useState } from "react";
import "../[fundId]/components/funds.css";
import {
  useNetAssetValueReportsGetFundNetAssetValuesChart,
  useNetAssetValueReportsGetFundNetAssetValueWithChanges,
  useNetAssetValueReportsGetFundTotalNetAssetValue,
} from "@workspace/investment-api/auto-generated/apis/net-asset-value-reports/net-asset-value-reports";
import Footer from "@/app/(home)/funds/[fundId]/components/Footer";
import { Skeleton } from "@workspace/ui/components/skeleton";
import IssuanceChart from "./components/issuance-chart";
import { FILTER_TIME, generateSeries } from "./components/issuance-chart/utils";
import { format } from "date-fns";
import { subtractMonthFromDate } from "@/utils/dateConverter";
import SwitchTabs from "@workspace/ui/components/switchTabs";
import {
  chartTypeItems,
  periodFilterItems,
} from "@/components/compare-funds/utils";

function FundDetail(params: any) {
  const fundId = params?.params?.params?.fundId;
  const [fromDate, setFromDate] = useState(
    format(subtractMonthFromDate(FILTER_TIME.Yearly), "yyyy-MM-dd"),
  );
  const netAssetparams = {
    FundId: fundId,
  };

  const {
    data: fundTotalnetAssetValue,
    isLoading: isFundTotalnetAssetValueLoading,
    isError: isFundTotalnetAssetValueError,
    refetch,
  } = useNetAssetValueReportsGetFundTotalNetAssetValue({ ...netAssetparams });

  const { data, isLoading, isError } = useFinancialInstitutesGetAll();
  const { data: chartData } = useNetAssetValueReportsGetFundNetAssetValuesChart(
    { FundId: fundId, FromDate: fromDate },
  );

  const { data: profileData } = useProfileGetLoggedInUserProfile();

  const {
    data: fundNetAssetValue,
    isLoading: fundNetAssetValueLoading,
    isError: isFundNetAssetValueError,
  } = useNetAssetValueReportsGetFundNetAssetValueWithChanges({
    ...netAssetparams,
  });

  const series = useMemo(() => {
    return generateSeries(chartData?.data?.fundNetAssetValues);
  }, [chartData]);

  const selectedFund = data?.data?.find((fund) => fund?.id === fundId);

  return (
    <div>
      {isLoading || isFundTotalnetAssetValueLoading ? (
        <Skeleton className="mb-6 h-[148px] rounded-t-none" />
      ) : (
        <FundCard
          logo={selectedFund?.logo}
          fullName={selectedFund?.fullName}
          fundsPageTags={selectedFund?.fundConfig?.detailPageTags}
          reportDate={fundTotalnetAssetValue?.data?.reportDate}
          fundTotalNetAssetValue={
            fundTotalnetAssetValue?.data?.fundTotalNetAssetValue
          }
          isFundTotalnetAssetValueError={isFundTotalnetAssetValueError}
          isError={isError}
          refetch={refetch}
        />
      )}
      {/* {profileData?.data?.role === RoleEnum.Customer && (
        <div className="px-4 pb-8">
          <MyProperty amount={452454} unit={145} />
        </div>
      )} */}
      <div>
        {fundNetAssetValueLoading ? (
          <div className="flex w-full gap-2 px-4 pb-4">
            <Skeleton className="h-16" />
            <Skeleton className="h-16" />
          </div>
        ) : (
          <div className="flex w-full gap-2 px-4 pb-4">
            <IssuanceCard
              title="قیمت صدور"
              changePercent={fundNetAssetValue?.data?.purchaseNavChangePercent}
              priceNumber={fundNetAssetValue?.data?.purchaseNav}
              isError={isFundNetAssetValueError}
            />
            <IssuanceCard
              title="قیمت ابطال"
              changePercent={fundNetAssetValue?.data?.saleNavChangePercent}
              priceNumber={fundNetAssetValue?.data?.saleNav}
              isError={isFundNetAssetValueError}
            />
          </div>
        )}
      </div>
      <div className="px-4 pb-4">
        <div className="bg-surface-nautral-default-4 rounded-lg p-2">
          <div className="pb-3">
            <SwitchTabs tabs={chartTypeItems} activeItemId="nav" />
          </div>
          <IssuanceChart series={series} time={fromDate} />
          <div className="pt-4">
            <SwitchTabs
              tabs={periodFilterItems}
              activeItemId={fromDate}
              onChange={(e) => {
                setFromDate(e as string);
              }}
            />
          </div>
        </div>
      </div>
      <RiskLevel />

      <Footer />
    </div>
  );
}

export default FundDetail;
