import React from "react";
import Info from "@/assets/icons/info.svg";
import { ProgressBar } from "@/components/progress-bar";

function RiskLevel() {
  return (
    <div className="bg-surface-nautral-active mx-4 rounded-lg p-2 pt-[9px]">
      <div className="flex items-start justify-between">
        <div className="text-text-nautral-default text-xs font-bold">
          میزان ریسک
        </div>
        <div className="bg-surface-nautral-default-3 rounded-sm p-1">
          <Info className="text-icon-neutral-default size-4" />
        </div>
      </div>
      <div className="text-text-nautral-secondary pt-3 pb-[22px] text-[10px]">
        مناسب برای افرادی که به دنبال یک سرمایه‌گذاری با نوسان متوسط و بازدهی
        ثابت هستند.
      </div>

      <ProgressBar status="low" />
    </div>
  );
}

export default RiskLevel;
