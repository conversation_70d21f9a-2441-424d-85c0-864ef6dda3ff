import useUserConfig from "@/hooks/useUserConfig";
import React from "react";
import Eye from "@/assets/icons/eye.svg";
import EyeLine from "@/assets/icons/eye-line.svg";
import "./funds.css";
import commaSeparator from "@workspace/ui/lib/commaSeparator";
import { IMyPropertyProps } from "@/app/(home)/funds/[fundId]/components/types";

function MyProperty({ unit, amount }: IMyPropertyProps) {
  const { configs, setConfig } = useUserConfig();

  const showSensitiveData = configs.showSensitiveData ?? false;

  return (
    <div className="myproperty-Pattern rounded-xl p-4">
      <div className="flex items-start justify-between">
        <div className="text-text-nautral-default text-xs font-bold">
          دارایی من
        </div>
        <div
          className="bg-surface-nautral-default-3 rounded-sm p-1"
          onClick={() => setConfig("showSensitiveData", !showSensitiveData)}
        >
          {showSensitiveData ? (
            <EyeLine className="size-4 cursor-pointer" />
          ) : (
            <Eye className="size-4 cursor-pointer" />
          )}
        </div>
      </div>

      <div className="bg-surface-nautral-modal-cover mt-4 rounded-sm px-2">
        <div className="border-surface-nautral-modal-cover flex items-center justify-between border-b pt-2 pb-[7.5px]">
          <div className="text-text-nautral-secondary text-[10px] leading-5">
            واحد
          </div>
          {showSensitiveData ? (
            <div className="text-xs font-bold">{unit}</div>
          ) : (
            <div className="leading-5">********</div>
          )}
        </div>
        <div className="flex items-center justify-between pt-[7.5px] pb-2">
          <div className="text-text-nautral-secondary text-[10px] leading-5">
            ریال
          </div>
          {showSensitiveData ? (
            <div className="text-xs font-bold">
              {commaSeparator(`${amount}`)}
            </div>
          ) : (
            <div className="leading-5">********</div>
          )}
        </div>
      </div>
    </div>
  );
}

export default MyProperty;
