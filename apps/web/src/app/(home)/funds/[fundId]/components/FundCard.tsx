import { formatNumber } from "@workspace/ui/lib/commaSeparator";
import React from "react";
import "./funds.css";
import { dateConverter } from "@/utils/dateConverter";
import Refresh from "@/assets/icons/refresh-2.svg";
import { IFundCardProps } from "@/app/(home)/funds/[fundId]/components/types";

function FundCard({
  logo,
  fullName,
  fundsPageTags,
  reportDate,
  fundTotalNetAssetValue,
  isFundTotalnetAssetValueError,
  isError,
  refetch,
}: IFundCardProps) {
  const handleClickRefresh = () => {
    refetch();
  };

  return (
    <div className="fundCard-bg-Pattern2 mb-6 rounded-b-2xl p-4">
      {isError ? (
        <div className="h-12" />
      ) : (
        <div className="flex items-center gap-2">
          <img alt="" src={`${logo}`} className="h-12 w-12" />
          <div className="flex flex-col gap-1">
            <div className="text-text-nautral-default text-sm leading-6">
              {fullName}
            </div>
            <div className="text-text-nautral-secondary flex gap-1 text-[10px]">
              {fundsPageTags?.map((i) => (
                <div className="bg-surface-nautral-transparent rounded-xs px-1 py-0.5 leading-4">
                  {i}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col gap-1 pt-4">
        <div className="text-text-primary-teritary flex cursor-pointer items-center justify-between px-2 text-[10px]">
          <div className="flex items-center gap-1">
            <Refresh className="size-2" />
            <div
              className="text-text-primary-teritary"
              onClick={() => handleClickRefresh()}
            >
              بروز رسانی
            </div>
          </div>
          <div>
            {isFundTotalnetAssetValueError
              ? ""
              : reportDate &&
                dateConverter(reportDate)
                  .locale("fa-IR")
                  .format("dddd DD MMMM YYYY")}
          </div>
        </div>
        <div className="bg-surface-nautral-transparent flex items-center justify-between gap-6 rounded-lg p-2">
          <div className="text-text-nautral-secondary text-[10px]">
            خالص ارزش دارایی
          </div>
          <div className="flex items-center gap-1">
            <div className="text-text-nautral-default pt-[1px] text-xs font-bold">
              {isFundTotalnetAssetValueError
                ? ""
                : fundTotalNetAssetValue &&
                  formatNumber(fundTotalNetAssetValue / 1000000000000, 2)}
            </div>
            <div className="text-text-nautral-secondary text-[10px]">
              هزار میلیارد ریال
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default FundCard;
