import Highcharts, { SeriesSplineOptions } from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { useMemo } from "react";
import { getChartOption } from "./utils";

interface IIssuanceChart {
  series: SeriesSplineOptions[];
  time?: string | number;
}

function IssuanceChart({ series }: IIssuanceChart) {
  const options = useMemo(() => getChartOption(series), [series]);

  return (
    <div className="chart-wrapper h-[160px] sm:h-full">
      <style>
        {`.highcharts-legend-item:not(:last-child)  > span {
        margin-left: 7px !important;}
        .highcharts-legend-item-hidden span {text-decoration: none !important;}`}
      </style>
      <HighchartsReact
        immutable
        containerProps={{
          className: "flex items-center justify-center h-full w-full",
        }}
        highcharts={Highcharts}
        options={options}
      />
    </div>
  );
}

export default IssuanceChart;
