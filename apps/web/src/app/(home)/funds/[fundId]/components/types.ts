export interface IFundCardProps {
  logo?: string | null;
  fullName?: string;
  fundsPageTags?: string[];
  reportDate?: string;
  fundTotalNetAssetValue?: number;
  isFundTotalnetAssetValueError: boolean;
  isError: boolean;
  refetch: () => void;
}

export interface IIssuanceCardProps {
  title: string;
  changePercent?: number;
  priceNumber?: number;
  isError: boolean;
}

export interface IMyPropertyProps {
  unit: number;
  amount: number;
}
