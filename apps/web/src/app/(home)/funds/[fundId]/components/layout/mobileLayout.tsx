import Header from "@/components/header";
import { useFinancialInstitutesGetAll } from "@workspace/investment-api/auto-generated/apis/financial-institutes/financial-institutes";
import { usePathname, useRouter } from "next/navigation";
import { ReactNode } from "react";
import ArrowRight from "@/assets/icons/arrow-right.svg";
import { ROUTE_HOME } from "@/constants/routes";

interface IMobileLayout {
  children: ReactNode;
  params: any;
}

function MobileLayout(props: IMobileLayout) {
  const router = useRouter();
  const pathname = usePathname();

  const { children, params } = props;

  const { data, isLoading } = useFinancialInstitutesGetAll();

  const selectedFund = data?.data?.find((fund) => fund?.id === params?.fundId);

  const handleClick = () => {
    router.push(ROUTE_HOME);
  };

  return (
    <div className="flex h-full flex-col">
      <Header
        title={
          isLoading
            ? ""
            : `${pathname?.includes("issuance") ? "درخواست صدور" : ""} ${selectedFund?.fullName}`
        }
        titleClassName="font-normal"
        center
        startAdornment={
          <div
            className="bg-surface-nautral-default-3 shrink-0 cursor-pointer rounded-sm p-2"
            onClick={handleClick}
          >
            <ArrowRight className="text-icon-primary-default size-4" />
          </div>
        }
      />
      <div className="flex-1">{children}</div>
    </div>
  );
}

export default MobileLayout;
