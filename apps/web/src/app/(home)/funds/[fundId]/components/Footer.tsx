import { useProfileGetLoggedInUserProfile } from "@workspace/investment-api/auto-generated/apis/profile/profile";
import { RoleEnum } from "@workspace/investment-api/auto-generated/models";
import { Button } from "@workspace/ui/components/button";
import useNoSejamAuthDrawer from "@/components/sejam-auth/useNoSejamAuth";
import { FUNDS_ISSUANCE, FUNDS_REVOKE } from "@/constants/routes";
import { usePathname, useRouter } from "next/navigation";

function Footer() {
  const router = useRouter();
  const pathname = usePathname();
  const { data: profileData } = useProfileGetLoggedInUserProfile();
  const { openNoSejamModal } = useNoSejamAuthDrawer();

  function handleIssuanceClick() {
    if (profileData?.data?.userProfile?.isActive) {
      router.push(`${pathname}/${FUNDS_ISSUANCE}`);
    } else {
      openNoSejamModal();
    }
  }

  function handleRevokeClick() {
    if (profileData?.data?.userProfile?.isActive) {
      router.push(`${pathname}/${FUNDS_REVOKE}`);
    } else {
      openNoSejamModal();
    }
  }

  return (
    <div className="bg-surface-nautral-default-2 shadow-Button fixed right-0 bottom-0 left-0 z-50 mx-auto max-w-2xl px-4 pt-2 pb-4">
      {/* {profileData?.data?.role === RoleEnum.Visitor && (
        <Button
          onClick={handleIssuanceClick}
          variant={"fill"}
          color={"success"}
          className="w-full"
        >
          صدور
        </Button>
      )} */}
      {/* {profileData?.data?.role === RoleEnum.Customer && (
        <div className="flex gap-2">
          <Button
            onClick={handleIssuanceClick}
            variant={"fill"}
            color={"success"}
            className="flex-1"
          >
            صدور
          </Button>
          <Button onClick={handleRevokeClick} variant={"fill"} color={"destructive"} className="flex-1">
            ابطال
          </Button>
        </div>
      )} */}
    </div>
  );
}

export default Footer;
