import React from "react";
import <PERSON> from "@/assets/icons/arrow-up.svg";
import { IIssuanceCardProps } from "@/app/(home)/funds/[fundId]/components/types";
import commaSeparator, { toDecimals } from "@workspace/ui/lib/commaSeparator";

function IssuanceCard({
  title,
  changePercent,
  priceNumber,
  isError,
}: IIssuanceCardProps) {
  return (
    <div className="bg-surface-nautral-default-4 flex w-1/2 flex-col gap-2 rounded-lg p-2">
      <div className="flex items-end justify-between">
        <div className="text-text-nautral-default text-xs">{title}</div>

        {changePercent && changePercent > 0 && (
          <div className="text-text-success-default flex items-center text-[10px] leading-4">
            {toDecimals(changePercent)}٪
            <Arrow className="size-3" />
          </div>
        )}

        {changePercent && changePercent < 0 && (
          <div className="text-text-error-default flex text-[10px]">
            {toDecimals(changePercent)}٪
            <Arrow className="size-3 rotate-180" />
          </div>
        )}
      </div>
      <div className="flex items-center justify-end gap-1">
        {isError ? (
          ""
        ) : (
          <div className="text-text-nautral-default text-sm leading-6">
            {commaSeparator(`${priceNumber}`)}
          </div>
        )}
        <div className="text-text-nautral-secondary text-[10px]">ریال</div>
      </div>
    </div>
  );
}

export default IssuanceCard;
