import { dateConverter } from "@/utils/dateConverter";
import { FundNetAssetValuesChartDto } from "@workspace/investment-api/auto-generated/models";
import { Options, SeriesSplineOptions } from "highcharts";
import commaSeparator from "@workspace/ui/lib/commaSeparator";

export enum FILTER_TIME {
  Yearly = 12,
  Month6 = 6,
  Month3 = 3,
  Month1 = 1,
}

const getTickInterval = (seriesLength: number) => {
  if (seriesLength < 34) {
    return 24 * 3600 * 1000;
  } else if (seriesLength < 185) {
    return 24 * 3600 * 1000 * 30;
  } else {
    return 24 * 3600 * 1000 * 50;
  }
};

export function generateSeries(
  data?: FundNetAssetValuesChartDto[],
): SeriesSplineOptions[] {
  return [
    {
      type: "spline",
      name: "ابطال",
      data: data?.map((item) => ({ x: item?.date, y: item?.saleNav })) as any,
      color: "#0FA968",
      legendIndex: 1,
    },
    {
      type: "spline",
      name: "صدور",
      color: "#E51D1D",
      legendIndex: 0,
      data: data?.map((item) => ({
        x: item?.date,
        y: item?.purchaseNav,
      })) as any,
    },
  ];
}

const getChartDateTimeBasedOnLength = (seriesLength: number) => {
  if (seriesLength < 34) {
    return "DD";
  } else if (seriesLength < 185) {
    return "MMMM";
  } else {
    return "YYYY";
  }
};

export const getChartOption = (series: SeriesSplineOptions[]): Options => {
  let year = "";
  return {
    series: series,
    xAxis: {
      type: "datetime",
      tickLength: 0,
      tickInterval: getTickInterval(Number(series?.[1]?.data?.length)),
      labels: {
        /* @ts-ignore */
        formatter() {
          /* @ts-ignore */
          const d = new Date(this.value) as any;
          const format = getChartDateTimeBasedOnLength(
            Number(series?.[1]?.data?.length),
          );
          const newYear = dateConverter(d).locale("fa-IR").format(format);
          const value = newYear !== year ? newYear : "";
          year = dateConverter(d).locale("fa-IR").format(format);
          return value;
        },
        style: {
          color: "#EFEFEF",
          fontSize: "10px",
        },
      },
    },
    yAxis: {
      title: {
        text: "",
      },
      gridLineColor: "var(--border-nautral-disable)",
      gridLineWidth: 1,
      labels: {
        enabled: false,
      },
    },
    chart: {
      backgroundColor: "transparent",
      plotBackgroundColor: "transparent",
      plotBorderWidth: 0,
      plotShadow: false,
      animation: false,
      margin: [0, 0, 27, 0],
      spacingTop: 0,
      spacingBottom: 0,
      spacingLeft: 0,
      spacingRight: 0,
    },
    tooltip: {
      /* @ts-ignore */
      useHTML: true,
      backgroundColor: "transparent",
      borderWidth: 0,
      shadow: false,
      /* @ts-ignore */
      // eslint-disable-next-line
      formatter: function () {
        let date1 = "";

        /* @ts-ignore */
        this.points?.forEach((i) => {
          const d = dateConverter(new Date(i.x).toString());
          date1 = d.format("YYYY/M/D");
        });

        let tooltipBody = `
              <div style="font-family: var(--font-yekan),serif !important" class="min-w-[120px] min-h-[60px] bg-surface-nautral-default-1 p-2 pt-1 rounded-lg text-text-nautral-secondary text-[8px] font-yekan flex flex-col justify-between gap-1">
                  <div class="text-center m-auto pb-1">${date1}</div>
              `;

        /* @ts-ignore */
        this.points?.forEach((i) => {
          const { y: value, series, color } = i;
          tooltipBody += `
                  <div class="flex justify-between">
                    <div style="direction: rtl">${commaSeparator(value ? value?.toString() : "")} ریال 
                    </div>
                  
                    <div class="flex">
<span>${series.name}</span>
                    
                    <div style="background-color: ${color}" class="w-2 h-2 rounded-[2px] ml-1"></div>
                    </div>
                  </div>
              `;
        });

        tooltipBody += "</div>";

        return tooltipBody;
      },
      shared: true,
    },
    plotOptions: {
      areaspline: {
        marker: {
          enabled: false,
          states: {
            hover: {
              enabled: true,
            },
          },
        },
      },
      spline: {
        marker: {
          enabled: false,
          states: {
            hover: {
              enabled: true,
            },
          },
        },
      },
    },
    title: { text: "" },
    subtitle: { text: "" },
    credits: { enabled: false },
    legend: {
      align: "right",
      verticalAlign: "top",
      x: 6,
      itemDistance: 12,
      floating: false,
      borderWidth: 0,
      backgroundColor: "transparent",
      symbolHeight: 0,
      symbolWidth: 0,
      useHTML: true,
      // @ts-ignore
      labelFormatter: function () {
        // @ts-ignore
        return `<div style="color: var(--text-nautral-default)" class="flex"><span class="text-[10px] relative" style="bottom: 3px;left: 4px ;font-family: var(--font-yekan),serif !important">${this.name}</span><div style="background-color: ${this?.visible ? this.color : "var(--border-nautral-disable)"}; border-radius: 4px; margin-left: 7px" class="w-3 h-3 inline-block"></div></div>`;
      },
    },
  };
};
