import React from "react";
import Arrow from "@/assets/icons/arrow-right.svg";
import { useResponsiveModal } from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import IntroductionModal from "@/app/(home)/funds/(funds)/components/IntroductionModal";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { useFinancialInstitutesGetAll } from "@workspace/investment-api/auto-generated/apis/financial-institutes/financial-institutes";
import "./funds.css";
import SelfFund from "@/app/(home)/components/funds/SelfFund";

function FundsList() {
  const { data, isLoading, isError } = useFinancialInstitutesGetAll();

  const { openModal, isMobile, closeModal } = useResponsiveModal();

  const handleIntroductionModal = () => {
    openModal({
      content: <IntroductionModal {...{ isMobile, closeModal }} />,
      id: "profile-modal",
      direction: "bottom", // Only affects mobile;
    });
  };

  if (isError) return null;

  return (
    <div className="p-4">
      <div
        className="relative flex h-16 justify-center gap-2 overflow-hidden rounded-lg"
        onClick={handleIntroductionModal}
      >
        <img
          src="/importanceOfInvestment.jpg"
          alt="Image"
          className="w-full object-cover"
        />
        <div className="introductionGradient absolute inset-0 rounded-lg"></div>
        <div className="r absolute mt-[25px] flex items-start justify-center gap-[9px] pr-[5px]">
          <div className="text-text-nautral-white text-center text-xs leading-4 font-bold">
            آشنایی با صندوق‌های سرمایه‌گذاری
          </div>

          <Arrow className="text-text-nautral-white h-4 w-4 rotate-180" />
        </div>
      </div>

      {isLoading ? (
        <Skeleton className="mt-6 h-[180px]" />
      ) : (
        <div className="mt-6">
          {data?.data?.map((item) => {
            return (
              <SelfFund
                item={item}
                hasArrow
                hasFooter
                className="shadow-shadow-fund-card fundCard-bg-Pattern2 rounded-2xl p-4"
                simpleReturnClassName="gap-[29.6px]"
              />
            );
          })}
        </div>
      )}
    </div>
  );
}

export default FundsList;
