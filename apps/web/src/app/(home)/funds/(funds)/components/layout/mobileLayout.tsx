import useProfileHook from "@/app/(home)/profile/useProfileHook";
import Header from "@/components/header";
import NavigationBar from "@/components/navigation-bar";
import { ROUTE_PROFILE } from "@/constants/routes";
import { RoleEnum } from "@workspace/investment-api/auto-generated/models";
import Link from "next/link";
import { ReactNode } from "react";

interface IMobileLayout {
  children: ReactNode;
}

function MobileLayout(props: IMobileLayout) {
  const { children } = props;

  const { data, isLoading } = useProfileHook();

  return (
    <div className="flex h-full flex-col">
      <Link href={ROUTE_PROFILE}>
        <Header
          title={
            // isLoading
            //   ? ""
            //   : data?.data?.role === RoleEnum.Customer
            //     ? `${data?.data?.userProfile?.userSejamProfile?.privatePerson?.firstName} ${
            //         data?.data?.userProfile?.userSejamProfile?.privatePerson
            //           ?.lastName
            //       }`
            //     : data?.data?.role === RoleEnum.Visitor
            //       ? data?.data?.visitorUserProfile?.mobileNumber
            //       : ""
            "new"
          }
          titleClassName="font-bold"
        />
      </Link>
      <div className="flex-1 pb-[168px]">{children}</div>
      <NavigationBar />
    </div>
  );
}

export default MobileLayout;
