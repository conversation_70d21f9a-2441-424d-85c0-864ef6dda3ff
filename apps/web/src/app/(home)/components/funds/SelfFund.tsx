import FundsCard from "@/app/(home)/components/funds/FundsCard";
import { ISelfFundProps } from "@/app/(home)/components/funds/types";
import { useSimpleReturnReportsGetFundTimePeriodSimpleReturns } from "@workspace/investment-api/auto-generated/apis/simple-return-reports/simple-return-reports";
import React from "react";

function SelfFund({
  item,
  hasFooter,
  hasArrow,
  className,
  simpleReturnClassName,
}: ISelfFundProps) {
  const {
    data: simpleReturnData,
    isLoading,
    isError,
  } = useSimpleReturnReportsGetFundTimePeriodSimpleReturns(item?.id || "", {
    query: { enabled: !!item?.id },
  });

  if (isError) return null;
  return (
    <FundsCard
      id={item?.id}
      logo={item?.logo}
      fullName={item?.fullName}
      fundsPageTags={item?.fundConfig?.fundsPageTags}
      className={className}
      monthlySimpleReturn={simpleReturnData?.data?.monthlySimpleReturn}
      threeMonthsSimpleReturn={simpleReturnData?.data?.threeMonthsSimpleReturn}
      sixMonthsSimpleReturn={simpleReturnData?.data?.sixMonthsSimpleReturn}
      yearlySimpleReturn={simpleReturnData?.data?.yearlySimpleReturn}
      hasFooter={hasFooter}
      hasArrow={hasArrow}
      isLoading={isLoading}
      simpleReturnClassName={simpleReturnClassName}
    />
  );
}

export default SelfFund;
