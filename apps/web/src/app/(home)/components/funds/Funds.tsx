import React from "react";
import { Auto<PERSON>, Mousewheel, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { cn } from "@workspace/ui/lib/utils";
import { useFundsHook } from "@/app/(home)/components/funds/useFundsHook";
import { Badge } from "@workspace/ui/components/badge";
import { useFinancialInstitutesGetAll } from "@workspace/investment-api/auto-generated/apis/financial-institutes/financial-institutes";
import "./funds.css";
import SelfFund from "@/app/(home)/components/funds/SelfFund";

function Funds() {
  const bannerSkeleton = Array.from({ length: 5 });

  const { data, isLoading, isError } = useFinancialInstitutesGetAll();

  const { filteredFundCards, setFilter, filter } = useFundsHook();

  if (isError) return null;

  return (
    <div className="bg-surface-nautral-transparent py-4 ps-4">
      <div className="pb-4 text-xs">صندوق‌ها</div>

      <div className="flex items-center gap-[5px]">
        <Badge
          state={filter === "all" ? "active" : "default"}
          onClick={() => setFilter("all")}
        >
          همه
        </Badge>
        <Badge
          state={filter === "fixed" ? "active" : "default"}
          onClick={() => setFilter("fixed")}
        >
          درآمد ثابت
        </Badge>
      </div>
      <div className="mt-2 h-[124px] w-full">
        <Swiper
          dir="rtl"
          spaceBetween={8}
          slidesPerView={1.18}
          loop={true}
          speed={700}
          grabCursor={true}
          autoplay={
            !isLoading
              ? {
                  delay: 6000,
                  disableOnInteraction: false,
                }
              : false
          }
          modules={[Navigation, Mousewheel, Autoplay]}
          className={cn("h-full w-full")}
        >
          {isLoading && bannerSkeleton && (
            <div className="h-full w-full">
              {bannerSkeleton.map(() => (
                <SwiperSlide>
                  <Skeleton />
                </SwiperSlide>
              ))}
            </div>
          )}

          {!isLoading && data?.data && data?.data?.length < 0 && ""}

          {!isLoading &&
            filteredFundCards &&
            filteredFundCards?.length > 0 &&
            filteredFundCards?.map((item: any, index) => (
              <SwiperSlide
                key={index}
                className="m-0 h-full w-full p-0 transition-transform duration-300 ease-in-out"
              >
                <SelfFund item={item} className="fundCard-bg-Pattern" />
              </SwiperSlide>
            ))}
        </Swiper>
      </div>
    </div>
  );
}

export default Funds;
