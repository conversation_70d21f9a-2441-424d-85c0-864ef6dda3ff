import { useFinancialInstitutesGetAll } from "@workspace/investment-api/auto-generated/apis/financial-institutes/financial-institutes";
import { useState } from "react";

const fundType = {
  FIXED_INCOME: 100, //درآمد ثابت
};

export const useFundsHook = () => {
  const { data } = useFinancialInstitutesGetAll();
  const [filter, setFilter] = useState<"all" | "fixed">("all");

  const filteredFundCards =
    filter === "all"
      ? data?.data
      : data?.data?.filter(
          (card) => card?.fundConfig?.fundType === fundType.FIXED_INCOME,
        );

  return { setFilter, filteredFundCards, filter };
};
