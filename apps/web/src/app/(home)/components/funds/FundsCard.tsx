import { cn, toFixed } from "@workspace/ui/lib/utils";
import Link from "next/link";
import React from "react";
import Arrow from "@/assets/icons/arrow-right.svg";
import commaSeparator from "@workspace/ui/lib/commaSeparator";
import { IFundsCardProps } from "@/app/(home)/components/funds/types";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { ROUTE_FUND_DETAIL } from "@/constants/routes";

function FundsCard({
  id,
  logo,
  fullName,
  fundsPageTags,
  monthlySimpleReturn,
  threeMonthsSimpleReturn,
  sixMonthsSimpleReturn,
  yearlySimpleReturn,
  hasFooter = false,
  hasArrow = false,
  className,
  simpleReturnClassName,
  isLoading,
}: IFundsCardProps) {
  return (
    <Link
      href={id ? ROUTE_FUND_DETAIL(id) : ""}
      className={cn("flex flex-col gap-2 rounded-lg p-2", className)}
    >
      <div className="flex justify-between">
        <div className="flex gap-2">
          <div>
            <img alt="" src={`${logo}`} className="h-[24px] w-auto" />
          </div>
          <div className="text-text-nautral-default text-sm leading-6">
            {fullName}
          </div>
        </div>

        {hasArrow && (
          <Arrow className="text-border-primary-default h-3 w-3 rotate-180" />
        )}
      </div>
      <div className="text-text-nautral-secondary flex gap-1 text-[10px]">
        {fundsPageTags?.map((i) => (
          <div className="bg-surface-nautral-transparent rounded-xs px-1 py-0.5 leading-4">
            {i}
          </div>
        ))}
      </div>

      {isLoading ? (
        <Skeleton className="h-10" />
      ) : (
        <div className={cn("mt-4 flex gap-6", simpleReturnClassName)}>
          <div>
            <div className="text-text-nautral-secondary text-[10px] leading-4">
              ۱ ماه اخیر
            </div>
            <div className="bg-surface-nautral-default-1 text-text-nautral-default flex items-end justify-center rounded-xs px-1 pt-1 text-xs leading-3">
              {monthlySimpleReturn && toFixed(monthlySimpleReturn, 1)}%
            </div>
          </div>

          <div>
            <div className="text-text-nautral-secondary text-[10px] leading-4">
              ۳ ماه اخیر
            </div>
            <div className="bg-surface-nautral-default-1 text-text-nautral-default flex items-center justify-center rounded-xs px-1 pt-1 text-xs leading-3">
              {threeMonthsSimpleReturn && toFixed(threeMonthsSimpleReturn, 1)}%
            </div>
          </div>
          <div>
            <div className="text-text-nautral-secondary text-[10px] leading-4">
              ۶ ماه اخیر
            </div>
            <div className="bg-surface-nautral-default-1 text-text-nautral-default flex items-center justify-center rounded-xs px-1 pt-1 text-xs leading-3">
              {sixMonthsSimpleReturn && toFixed(sixMonthsSimpleReturn, 1)}%
            </div>
          </div>
          <div>
            <div className="text-text-nautral-secondary text-[10px] leading-4">
              ۱۲ ماه اخیر
            </div>
            <div className="bg-surface-nautral-default-1 text-text-nautral-default flex items-center justify-center rounded-xs px-1 pt-1 text-xs leading-3">
              {yearlySimpleReturn && toFixed(yearlySimpleReturn, 1)}%
            </div>
          </div>
        </div>
      )}

      {hasFooter && (
        <div className="bg-surface-nautral-transparent flex items-center gap-6 rounded-t-xs rounded-b-lg p-2">
          <div className="text-text-nautral-secondary text-[10px]">
            خالص ارزش دارایی صندوق
          </div>
          <div className="flex items-center gap-1">
            <div className="text-text-nautral-default pt-[1px] text-xs font-bold">
              {commaSeparator("4520")}
            </div>
            <div className="text-text-nautral-secondary text-[10px]">
              هزار میلیارد ریال
            </div>
          </div>
        </div>
      )}
    </Link>
  );
}

export default FundsCard;
