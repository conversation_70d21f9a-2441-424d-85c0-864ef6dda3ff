// import { GetAllFinancialInstitutesResponse } from "@workspace/investment-api/auto-generated/models";
// Todo: check new type


export interface IFundsCardProps {
  hasFooter?: boolean;
  hasArrow?: boolean;
  className?: string;
  simpleReturnClassName?: string;
  isLoading?: boolean;
  logo?: string | null;
  fullName?: string;
  fundsPageTags?: string[];
  monthlySimpleReturn?: number;
  threeMonthsSimpleReturn?: number;
  sixMonthsSimpleReturn?: number;
  yearlySimpleReturn?: number;
  id?: string;
}

export interface ISelfFundProps {
  item: any;
  hasFooter?: boolean;
  hasArrow?: boolean;
  className?: string;
  simpleReturnClassName?: string;
}
