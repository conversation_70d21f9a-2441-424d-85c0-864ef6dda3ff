import React from "react";
import { Autoplay, Mousewheel, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import Styles from "./banner.module.scss";
import { bindImageUrl } from "@/constants/fileManager";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { cn } from "@workspace/ui/lib/utils";
import Link from "next/link";
import { useBannerHook } from "@/app/(home)/components/banner/useBannerHook";

function BannerSlider() {
  const bannerSkeleton = Array.from({ length: 5 });

  const { data, isLoading, isError } = useBannerHook();

  if (isError) return null;

  return (
    <div className="my-8 h-[158px] w-full">
      <Swiper
        dir="rtl"
        spaceBetween={-8}
        slidesPerView={1.11}
        centeredSlides={true}
        loop={true}
        speed={700}
        grabCursor={true}
        autoplay={
          !isLoading
            ? {
                delay: 6000,
                disableOnInteraction: false,
              }
            : false
        }
        modules={[Navigation, Mousewheel, Autoplay]}
        className={cn(Styles.root, "h-full w-full")}
      >
        {isLoading && bannerSkeleton && (
          <div className="h-full w-full">
            {bannerSkeleton.map(() => (
              <SwiperSlide>
                <Skeleton />
              </SwiperSlide>
            ))}
          </div>
        )}

        {!isLoading && data?.data && data?.data?.length < 0 && ""}

        {!isLoading &&
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.map((item, index) => (
            <SwiperSlide
              key={index}
              className="m-0 h-full w-full p-0 transition-transform duration-300 ease-in-out"
            >
              <Link
                target="_blank"
                rel="noreferrer"
                href={item?.linkText || ""}
              >
                <img
                  alt="banner"
                  src={item?.imageUrl && bindImageUrl(item?.imageUrl)}
                  className="h-[158px] w-full rounded-lg object-cover"
                  style={{ pointerEvents: "none", margin: 0, padding: 0 }}
                />
              </Link>
            </SwiperSlide>
          ))}
      </Swiper>
    </div>
  );
}

export default BannerSlider;
