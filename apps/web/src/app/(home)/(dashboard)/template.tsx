"use client";

import Header from "@/components/header";
import Navigation<PERSON><PERSON> from "@/components/navigation-bar";
import { ROUTE_PROFILE } from "@/constants/routes";
import Link from "next/link";
import { ReactNode } from "react";
import Question from "@/assets/icons/question.svg";
import { useProfileGetLoggedInUserProfile } from "@workspace/investment-api/auto-generated/apis/profile/profile";
import useUserConfig from "@/hooks/useUserConfig";
import useSejamAuthDrawer from "@/components/sejam-auth/useSejamAuthDrawer";

function Template({ children }: { children: ReactNode }) {
  const { data: profile } = useProfileGetLoggedInUserProfile();
  useUserConfig();

  const isVisitor = !!profile?.data?.visitorUserProfile?.visitorUserId;

  const title =
    profile?.data?.userProfile?.sejamProfiles?.[0]?.firstName &&
    profile?.data?.userProfile?.sejamProfiles?.[0]?.lastName
      ? `${profile.data.userProfile.sejamProfiles?.[0].firstName} ${profile.data.userProfile.sejamProfiles?.[0].lastName}`
      : profile?.data?.visitorUserProfile?.mobileNumber;
  const { openSejamModal } = useSejamAuthDrawer();

  return (
    <div className="flex h-full flex-col">
      <Link href={ROUTE_PROFILE}>
        <Header
          title={title}
          subTitle={
            isVisitor ? (
              <span
                className="text-text-error-default-2 text-[10px]"
                onClick={openSejamModal}
              >
                هنوز احراز هویت نکرده‌اید
              </span>
            ) : (
              ""
            )
          }
          endAdornment={
            <div
              onClick={(e) => {
                e.preventDefault();
              }}
              className="bg-surface-nautral-default-3 shrink-0 rounded-sm p-2"
            >
              <Question className="text-icon-primary-default size-4" />
            </div>
          }
        />
      </Link>
      {children}
      <NavigationBar />
    </div>
  );
}

export default Template;
