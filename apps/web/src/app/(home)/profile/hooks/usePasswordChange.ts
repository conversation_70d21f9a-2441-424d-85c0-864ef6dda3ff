import {
  useChangePasswordChangeActorUserPassword,
  useChangePasswordCheckActorUserPassword,
} from "@workspace/investment-api/auto-generated/apis/change-password/change-password";
import useCreatePasswordModal from "@/app/(home)/profile/hooks/useCreatePasswordModal";
import useSuccessPasswordChangeModal from "@/app/(home)/profile/hooks/useSuccessPasswordChangeModal";
import { ICreatPasswordSubmit } from "@/components/create-password/types";
import { toast } from "@workspace/ui/components/toast";

function usePasswordChange() {
  const { mutateAsync: changePasswordCheckActorUserPassword } =
    useChangePasswordCheckActorUserPassword();
  const { openCreatePasswordModal, closeModal } = useCreatePasswordModal();
  const { openSuccessModal } = useSuccessPasswordChangeModal();
  const { mutateAsync: changeActorUserPassword } =
    useChangePasswordChangeActorUserPassword();
  const submitNewPassword = (data: ICreatPasswordSubmit) => {
    const body = {
      ...data,
      newPassword: data?.password,
      confirmNewPassword: data?.confirmPassword,
    };
    changeActorUserPassword({ data: body })
      .then((e) => {
        if (e?.data) {
          closeModal();
          openSuccessModal();
        }
      })
      .catch((error) => {
        toast.error(
          error?.response?.data?.errorMessage ||
            "لطفا چند لحظه دیگر تلاش کنید.",
        );
      });
  };
  const checkPassword = async (password: string) => {
    return changePasswordCheckActorUserPassword({ data: { password } })
      .then((res) => {
        if (res?.data) {
          openCreatePasswordModal((e) =>
            submitNewPassword({ ...e, oldPassword: password }),
          );
        }
      })
      .catch((error) => {
        toast.error(
          error?.response?.data?.errorMessage ||
            "لطفا چند لحظه دیگر تلاش کنید.",
        );
      });
  };

  return { checkPassword };
}

export default usePasswordChange;
