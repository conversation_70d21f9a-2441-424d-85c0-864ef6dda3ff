"use client";
import { useResponsiveModal } from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import { CreatePassword } from "@/components/sejam-auth/components";
import { ICreatPasswordSubmit } from "@/components/create-password/types";

const useCreatePasswordModal = () => {
  const { openModal, closeModal } = useResponsiveModal();
  const createPassId = `create-pass-modal-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  function handleCloseModal() {
    closeModal(createPassId);
  }
  const openCreatePasswordModal = (
    onSubmit: (data: ICreatPasswordSubmit) => void,
  ) => {
    return openModal({
      id: createPassId,
      content: <CreatePassword onSubmit={onSubmit} />,
    });
  };

  return { openCreatePasswordModal, closeModal: handleCloseModal };
};

export default useCreatePasswordModal;
