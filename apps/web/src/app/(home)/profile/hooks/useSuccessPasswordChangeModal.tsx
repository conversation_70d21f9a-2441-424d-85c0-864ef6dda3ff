"use client";
import { useResponsiveModal } from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import { SuccessPasswordChange } from "../components/success-password-change";

const useSuccessPasswordChangeModal = () => {
  const { openModal } = useResponsiveModal();

  const openSuccessModal = () => {
    return openModal({
      id: `success-modal-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      content: <SuccessPasswordChange />,
    });
  };

  return { openSuccessModal };
};

export default useSuccessPasswordChangeModal;
