"use client";

import MobileLayout from "@/app/(home)/profile/components/layout/mobileLayout";
import { ResponsiveRenderer } from "@workspace/ui/components/responsive-renderer";

interface ILayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: ILayoutProps) {
  return (
    <ResponsiveRenderer
      desktop={<MobileLayout>{children}</MobileLayout>}
      mobile={<MobileLayout>{children}</MobileLayout>}
    />
  );
}
