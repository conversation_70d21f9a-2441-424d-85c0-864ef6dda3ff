"use client";

import React from "react";
import useProfileHook from "@/app/(home)/profile/useProfileHook";
import VisitorUserInformation from "@/app/(home)/profile/components/VisitorUserInformation";
import Report from "@/app/(home)/profile/components/Report";
import Setting from "@/app/(home)/profile/components/Setting";
import ProfileHeader from "@/app/(home)/profile/components/ProfileHeader";
import ExitConfirmModal from "@/app/(home)/profile/components/ExitConfirmModal";
import CustomerUserInformation from "@/app/(home)/profile/components/CustomerUserInformation";
import { RoleEnum } from "@workspace/investment-api/auto-generated/models";
import { Loading } from "@workspace/ui/components/loading";
import { Button } from "@workspace/ui/components/button";
import { useResponsiveModal } from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import Logout from "@/assets/icons/logout.svg";
import DarkMode from "@/app/(home)/profile/components/DarkMode";

function Profile() {
  const { data, isLoading } = useProfileHook();

  const { openModal, isMobile, closeModal } = useResponsiveModal();

  const handleExitModal = () => {
    openModal({
      content: <ExitConfirmModal {...{ isMobile, closeModal }} />,
      id: "profile-modal",
      direction: "bottom", // Only affects mobile;
    });
  };

  if (isLoading) {
    return (
      <div className="flex h-full flex-1 items-center justify-center">
        <Loading />
      </div>
    );
  }

  return null;
}

export default Profile;
