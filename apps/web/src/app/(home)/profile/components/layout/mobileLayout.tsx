import ArrowRight from "@/assets/icons/arrow-right.svg";
import Header from "@/components/header";
import NavigationBar from "@/components/navigation-bar";
import { useRouter } from "next/navigation";
import { ReactNode } from "react";

interface IMobileLayout {
  children: ReactNode;
}

function MobileLayout(props: IMobileLayout) {
  const { children } = props;
  const router = useRouter();

  const handleClick = () => {
    router.back();
  };

  return (
    <div className="flex h-full flex-col">
      <Header
        title="اطلاعات و تنظیمات کاربر"
        titleClassName="font-normal"
        center
        startAdornment={
          <div
            className="bg-surface-nautral-default-3 shrink-0 cursor-pointer rounded-sm p-2"
            onClick={handleClick}
          >
            <ArrowRight className="text-icon-primary-default size-4" />
          </div>
        }
      />
      <div className="flex-1 pb-[168px]">{children}</div>
      <NavigationBar />
    </div>
  );
}

export default MobileLayout;
