"use client";
import { DrawerTitle } from "@workspace/ui/components/drawer";
import Check from "@/assets/icons/Check.svg";
import { Button } from "@workspace/ui/components/button";
import { useLogout } from "@/hooks/useLogout";

export function SuccessPasswordChange() {
  const { logout } = useLogout();

  return (
    <>
      <DrawerTitle />
      <div className="bg-background-nautral-body flex flex-col items-center justify-between p-4 align-middle">
        <div className="flex w-full flex-col items-center justify-center pt-3 pb-[18px] text-sm">
          <div className="bg-surface-success-default flex h-16 w-16 rounded-2xl">
            <Check className="text-icon-success-default m-auto w-12" />
          </div>
          <div className="text-text-success-default pt-4 pb-6 text-center">
            رمزعبور با موفقیت تغییر کرد{" "}
          </div>
        </div>
        <div className="flex w-full">
          <Button
            className="mt-4 w-full text-xs"
            variant="fill"
            size="md"
            onClick={logout}
          >
            ورود با رمز جدید
          </Button>
        </div>
      </div>
    </>
  );
}
