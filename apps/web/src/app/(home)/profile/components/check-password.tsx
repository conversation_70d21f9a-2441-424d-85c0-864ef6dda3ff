import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { Loading } from "@workspace/ui/components/loading";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { InputPassword } from "@workspace/ui/components/input-password";

const formSchema = z.object({
  password: z.string().nonempty("رمز عبور را وارد کنید."),
});

export type LoginByPasswordHandleSubmitFunction = (
  password: string,
) => Promise<unknown>;

interface CheckPasswordProps {
  handleSubmit: LoginByPasswordHandleSubmitFunction;
}

function CheckPassword({ handleSubmit }: CheckPasswordProps) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    return handleSubmit(values.password);
  }

  return (
    <div className="flex flex-1 flex-col justify-between gap-8">
      <div className="flex flex-col justify-between gap-8.5 pt-2">
        <div className="text-text-nautral-default w-full text-sm text-nowrap">
          برای تغییر رمز ورود ابتدا رمز ورود فعلی خود
          <br />
          را وارد کنید.
        </div>

        <Form {...form}>
          <form
            id="check-password-form"
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col justify-between"
          >
            <div className="flex flex-col gap-4">
              <FormField
                control={form.control}
                name="password"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <InputPassword
                        size="lg"
                        title="رمز فعلی"
                        {...field}
                        helperText={fieldState?.error?.message}
                        aria-invalid={!!fieldState?.error?.message}
                        name="password"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
      </div>

      <Button
        variant="fill"
        size="md"
        type="submit"
        form="check-password-form"
        disabled={form.formState.isSubmitting}
        endAdornment={form.formState.isSubmitting && <Loading size="sm" />}
      >
        تایید
      </Button>
    </div>
  );
}
export default CheckPassword;
