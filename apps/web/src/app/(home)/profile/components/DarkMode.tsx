import React from "react";
import {
  RadioGroup,
  RadioGroupItem,
} from "@workspace/ui/components/radio-group";
import useUserConfig from "@/hooks/useUserConfig";
import { Loading } from "@workspace/ui/components/loading";
import { useForm } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  Form,
} from "@workspace/ui/components/form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

function DarkMode() {
  const { configs, setConfig, isLoading } = useUserConfig();

  const formSchema = z.object({
    darkMode: z.union([
      z.literal("dark"),
      z.literal("light"),
      z.literal("system"),
    ]),
  });

  type DarkModeFormValues = z.infer<typeof formSchema>;

  const form = useForm<DarkModeFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      darkMode: configs.darkMode || "system",
    },
    mode: "onChange",
  });

  // This function will be called on form submission
  const onSubmit = async (values: DarkModeFormValues) => {
    try {
      await setConfig("darkMode", values.darkMode);
    } catch (error) {
      console.error("Failed to save theme:", error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-[308px] items-center justify-center">
        <Loading />
      </div>
    );
  }

  return (
    <div>
      <Form {...form}>
        <form
          id="theme-form"
          className="flex flex-col justify-between pt-4 pb-2"
          onSubmit={form.handleSubmit(onSubmit)}
        >
          <div className="flex flex-col justify-between gap-6">
            <FormField
              control={form.control}
              name="darkMode"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <RadioGroup
                      {...field}
                      className="gap-6"
                      value={field.value}
                      onValueChange={(value) => {
                        field.onChange(value);
                        form.handleSubmit(onSubmit)();
                      }}
                    >
                      <RadioGroupItem
                        value="system"
                        id="r3"
                        label="خودکار- مطابق سیستم عامل"
                      />
                      <RadioGroupItem value="light" id="r1" label="روشن" />
                      <RadioGroupItem value="dark" id="r2" label="تیره" />
                    </RadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </form>
      </Form>
    </div>
  );
}

export default DarkMode;
