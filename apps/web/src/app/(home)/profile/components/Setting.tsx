import {
  Accordion,
  AccordionContent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/accordion/accordion";
import React from "react";
import Monitor from "@/assets/icons/monitor-mobbile.svg";
import <PERSON> from "@/assets/icons/moon.svg";
import Lock from "@/assets/icons/lock.svg";
import Document from "@/assets/icons/document-text.svg";
import useProfileHook from "@/app/(home)/profile/useProfileHook";
import { RoleEnum } from "@workspace/investment-api/auto-generated/models";
import useUserConfig from "@/hooks/useUserConfig";
import CheckPassword from "@/app/(home)/profile/components/check-password";
import usePasswordChange from "@/app/(home)/profile/hooks/usePasswordChange";
import DarkMode from "@/app/(home)/profile/components/DarkMode";

function Setting() {
  const { data } = useProfileHook();
  const { configs } = useUserConfig();
  const { checkPassword } = usePasswordChange();

  return null;
}

export default Setting;
