import useProfileHook from "@/app/(home)/profile/useProfileHook";
import { dateConverter } from "@/utils/dateConverter";
import { Loading } from "@workspace/ui/components/loading";
import React from "react";
import useSejamAuthDrawer from "@/components/sejam-auth/useSejamAuthDrawer";

function VisitorUserInformation() {
  const { data, isLoading } = useProfileHook();
  const { openSejamModal } = useSejamAuthDrawer();

  if (isLoading) {
    return (
      <div className="bg-surface-nautral-default-4 flex flex-col items-center justify-center rounded-lg py-10">
        <Loading />
      </div>
    );
  }

  return (
    <div className="bg-surface-nautral-default-4 flex flex-col gap-4 rounded-lg p-4">
      <div className="flex flex-col gap-1">
        <div className="text-text-nautral-secondary text-xs">وضعیت حساب</div>
        <div className="text-text-success-default text-sm leading-6">فعال</div>
      </div>
      <div className="flex flex-col gap-1">
        <div className="text-text-nautral-secondary text-xs">
          تاریخ احراز هویت
        </div>
        <div
          onClick={() => openSejamModal()}
          className="text-text-warning-default cursor-pointer text-sm leading-6"
        >
          احراز هویت نشده
        </div>
      </div>
      <div className="border-border-nautral-disable flex flex-col gap-1 border-b pb-4">
        <div className="text-text-nautral-secondary text-xs">تاریخ ثبت نام</div>
        {data?.data?.visitorUserProfile?.createDate ? (
          <div className="text-text-nautral-default rtl text-sm leading-6">
            {dateConverter(data?.data?.visitorUserProfile?.createDate)
              .locale("fa-IR")
              .format("DD MMMM YYYY")}
          </div>
        ) : (
          "-"
        )}
      </div>
      <div className="-mt-[1px] flex flex-col gap-1">
        <div className="text-text-nautral-secondary text-xs">شماره موبایل</div>
        <div className="text-text-nautral-default text-sm leading-6">
          {data?.data?.visitorUserProfile?.mobileNumber || "-"}
        </div>
      </div>
    </div>
  );
}

export default VisitorUserInformation;
