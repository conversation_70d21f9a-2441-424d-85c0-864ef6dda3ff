import React from "react";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
} from "@/components/accordion/accordion";
import User from "@/assets/icons/user-square.svg";
import Card from "@/assets/icons/card.svg";
import PrivatePerson from "@/app/(home)/profile/components/customerUserInformationContent/PrivatePerson";
import AccountInformation from "@/app/(home)/profile/components/customerUserInformationContent/AccountInformation";
import { AccordionContent } from "@radix-ui/react-accordion";

function CustomerUserInformation() {
  return (
    <Accordion type="single" collapsible className="flex w-full flex-col gap-2">
      <AccordionItem value="item-1">
        <AccordionTrigger
          className="text-text-nautral-secondary font-bold"
          startAdornment={
            <User className="text-icon-neutral-secondary size-6" />
          }
        >
          اطلاعات هویتی کاربر
        </AccordionTrigger>
        <AccordionContent className="flex flex-col gap-4 py-[17px] text-balance">
          <PrivatePerson />
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value="item-2">
        <AccordionTrigger
          className="text-text-nautral-secondary font-bold"
          startAdornment={
            <Card className="text-icon-neutral-secondary size-6" />
          }
        >
          اطلاعات حساب بانکی
        </AccordionTrigger>
        <AccordionContent className="flex flex-col gap-4 py-4 text-balance">
          <AccountInformation />
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}

export default CustomerUserInformation;
