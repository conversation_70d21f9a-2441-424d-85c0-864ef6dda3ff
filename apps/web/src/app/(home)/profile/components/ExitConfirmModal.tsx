import { Button } from "@workspace/ui/components/button";
import React from "react";
import Logout from "@/assets/icons/logout2.svg";
import {
  DrawerClose,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@workspace/ui/components/drawer";
import {
  DialogClose,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { useLogout } from "@/hooks/useLogout";

interface ExitConfirmModalProps {
  closeModal: (id: string) => void;
  isMobile: boolean;
}

function ExitConfirmModal({ closeModal, isMobile }: ExitConfirmModalProps) {
  const { logout } = useLogout();

  return isMobile ? (
    // Mobile: Use Drawer components
    <div>
      <DrawerHeader className="pt-6 pb-0">
        <DrawerTitle className="border-surface-nautral-modal-cover border-b pb-[7px] !text-xs font-normal">
          خروج از حساب کاربری
        </DrawerTitle>
      </DrawerHeader>
      <div className="flex flex-col items-center justify-center gap-4 px-4 pt-6 pb-10">
        <Logout className="text-icon-neutral-disable size-12" />
        <div className="text-text-nautral-secondary text-sm leading-[22px]">
          می‌خواهید از حساب کاربری خود خارج شوید؟
        </div>
      </div>
      <DrawerFooter>
        <div className="flex w-full gap-4">
          <DrawerClose asChild>
            <Button
              onClick={logout}
              className="flex-1 text-base"
              variant="outline"
              color={"destructive"}
            >
              خارج می‌شوم
            </Button>
          </DrawerClose>
          <DrawerClose asChild>
            <Button
              variant={"outline"}
              color="default"
              onClick={() => closeModal("profile-modal")}
              className="flex-1 text-base"
            >
              انصراف
            </Button>
          </DrawerClose>
        </div>
      </DrawerFooter>
    </div>
  ) : (
    // Desktop: Use Dialog components
    <div>
      <DialogHeader>
        <DialogTitle>خروج از حساب کاربری</DialogTitle>
      </DialogHeader>
      <div className="flex flex-col items-center justify-center gap-4 px-4 pt-6 pb-10">
        <Logout className="size-12" />
        <div className="text-text-nautral-secondary text-sm leading-[22px]">
          می‌خواهید از حساب کاربری خود خارج شوید؟
        </div>
      </div>
      <DialogFooter>
        <div className="flex w-full gap-4">
          <DialogClose asChild>
            <Button
              variant={"outline"}
              color="default"
              onClick={() => closeModal("profile-modal2")}
              className="flex-1"
            >
              انصراف
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button
              onClick={logout}
              className="flex-1"
              variant="outline"
              color={"destructive"}
            >
              خارج می‌شوم
            </Button>
          </DialogClose>
        </div>
      </DialogFooter>
    </div>
  );
}
export default ExitConfirmModal;
