import React from "react";

import User from "@/assets/icons/User.svg";
import useProfileHook from "@/app/(home)/profile/useProfileHook";
import useUserConfig from "@/hooks/useUserConfig";
import { RoleEnum } from "@workspace/investment-api/auto-generated/models";
import { Loading } from "@workspace/ui/components/loading";
import commaSeparator from "@workspace/ui/lib/commaSeparator";
import Eye from "@/assets/icons/eye.svg";
import EyeLine from "@/assets/icons/eye-line.svg";

function ProfileHeader() {
  const { data, isLoading: profileLoading } = useProfileHook();

  const { configs, setConfig, isLoading } = useUserConfig();

  const showSensitiveData = configs.showSensitiveData ?? false;

  const stockExchangeType = "StockExchange";

  // const selectedTradingCode =
  //   data?.data?.userProfile?.userSejamProfile?.tradingCodes?.find(
  //     (item) => item.type === stockExchangeType,
  //   );

  if (profileLoading || isLoading) {
    return (
      <div className="bg-surface-nautral-default-3 dark:shadow-top-fix-card flex items-center justify-center rounded-b-2xl py-14">
        <Loading />
      </div>
    );
  }

  return null;
}

export default ProfileHeader;
