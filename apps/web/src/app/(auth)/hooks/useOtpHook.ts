import { THREE_TIMES_TRY_ERROR } from "@/app/(auth)/utils";
import { useAuthSendLoginOTP } from "@workspace/investment-api/auto-generated/apis/auth/auth";
import { ApiResultOfGetOtpResponse } from "@workspace/investment-api/auto-generated/models";
import { toast } from "@workspace/ui/components/toast";
import { useState } from "react";

export type OnSendOtpFunc = {
  sk?: string;
  enabledLockedError?: boolean;
};

export type OTPHookReturnType = {
  onSendOtp: (params: OnSendOtpFunc) => Promise<ApiResultOfGetOtpResponse>;
  isLoadingSendOtp: boolean;
  otpSendDate: string;
  otpExpirationDate: string;
};

export const useOtpHook = (): OTPHookReturnType => {
  const [otpSendDate, setOtpSendDate] = useState<string>("");
  const [otpExpirationDate, setOtpExpirationDate] = useState<string>("");
  const errorTile = (errorCode?: string) =>
    errorCode && THREE_TIMES_TRY_ERROR.includes(errorCode)
      ? "حساب شما مسدود است!"
      : " ";

  const loginMethodHeaders = {
    "Content-Type": "multipart/form-data",
  };

  const { mutateAsync: sendLoginOtpMutate, isPending: isLoadingSendOtp } =
    useAuthSendLoginOTP({
      request: {
        headers: loginMethodHeaders,
      },
      mutation: { retry: 0 },
    });

  const onSendOtp = async ({ sk }: OnSendOtpFunc) => {
    return await sendLoginOtpMutate(
      { data: { secretKey: sk } },
      {
        onSuccess: (data) => {
          if (data?.data?.otpSendDate) setOtpSendDate(data?.data?.otpSendDate);
          if (data?.data?.otpExpirationDate)
            setOtpExpirationDate(data?.data?.otpExpirationDate);
        },
        onError: (error) => {
          const errorData = error.response?.data as
            | { errorMessage?: string; errorCode?: string }
            | undefined;
          if (errorData?.errorMessage) {
            const hasErrorTitle = errorTile(errorData.errorCode);
            toast.error(
              hasErrorTitle
                ? errorTile(errorData.errorCode)
                : errorData.errorMessage,
              {
                description: hasErrorTitle ? errorData.errorMessage : undefined,
              },
            );
          }
        },
      },
    );
  };

  return { onSendOtp, isLoadingSendOtp, otpSendDate, otpExpirationDate };
};
