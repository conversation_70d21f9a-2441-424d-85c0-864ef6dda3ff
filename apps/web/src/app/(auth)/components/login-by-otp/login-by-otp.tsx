import React from "react";
import { ResponsiveRenderer } from "@workspace/ui/components/responsive-renderer";
import MobileLoginByOtp, {
  LoginByOtpHandleSubmitFunction,
} from "@/app/(auth)/components/login-by-otp/mobile/mobile-login-by-otp";
import { OTPHookReturnType } from "@/app/(auth)/hooks/useOtpHook";

interface ILoginByOtpProps {
  handleSubmitOtp: LoginByOtpHandleSubmitFunction;
}

function LoginByOtp({ handleSubmitOtp }: ILoginByOtpProps) {
  return (
    <ResponsiveRenderer
      desktop={<MobileLoginByOtp handleSubmit={handleSubmitOtp} />}
      mobile={<MobileLoginByOtp handleSubmit={handleSubmitOtp} />}
    />
  );
}

export default LoginByOtp;
