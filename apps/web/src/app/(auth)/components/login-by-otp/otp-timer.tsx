/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/require-default-props */
/* eslint-disable no-underscore-dangle */
/* eslint-disable @typescript-eslint/naming-convention */
import isNull from "lodash/isNull";
import { useEffect, useState } from "react";
import RefreshIcon from "@/assets/icons/reload.svg";
import { useInterval } from "@/hooks/useInterval";
import { cn } from "@workspace/ui/lib/utils";
import { Loading } from "@workspace/ui/components/loading";

const _second = 1000;
const _minute = _second * 60;
const _hour = _minute * 60;

export interface IOtpTimer {
  isLoading?: boolean;
  otpSendDate: string | number;
  expirationDate: string | number;
  className?: string;
  onResend?: () => void;
}

function OtpTimer({
  otpSendDate,
  expirationDate,
  isLoading,
  className = "min-h-8",
  onResend,
}: IOtpTimer) {
  const [elapsedSeconds, setElapsedSeconds] = useState<number | null>(null);

  /* ----------------------------- initialing time ----------------------------- */
  const expirationTime = new Date(expirationDate);
  const sendTime = new Date(otpSendDate);

  const otpValidityDuration = expirationTime.getTime() - sendTime.getTime();

  const minutes = elapsedSeconds
    ? Math.floor((elapsedSeconds % _hour) / _minute)
    : 0;
  const seconds = elapsedSeconds
    ? Math.floor((elapsedSeconds % _minute) / _second)
    : 0;

  /* -------------------- resend phone number & reset time -------------------- */
  const handleResend = () => {
    onResend?.();
  };

  /* ------------------ initializing time on prop changes ------------------ */
  useEffect(() => {
    const now = new Date().getTime();
    const sendTimestamp = sendTime.getTime();
    const validity = expirationTime.getTime() - sendTimestamp;
    const elapsed = validity - (now - sendTimestamp);

    setElapsedSeconds(Math.max(elapsed, 0));
  }, [otpSendDate, expirationDate]);

  useInterval(
    () => {
      setElapsedSeconds((prev) => {
        if (prev === null || prev <= 0) return 0;
        return prev - 1000;
      });
    },
    isNull(elapsedSeconds) || elapsedSeconds > 0 ? 1000 : null,
  );

  if (elapsedSeconds === null && !isLoading)
    return (
      <div className="flex h-8 w-full justify-center">
        <Loading size="sm" />
      </div>
    );

  const isExpired =
    !isLoading && !isNull(elapsedSeconds) && elapsedSeconds <= 0;

  return (
    <div className="flex h-8 w-full justify-center">
      <button
        type="button"
        disabled={isLoading || !isExpired}
        onClick={handleResend}
        data-test="46c647b2-caac-4626-8299-cc2557104a50"
        className={cn(
          "text-headerBlack xxl:text-sm flex min-h-8 cursor-pointer items-center gap-1 bg-transparent text-xs leading-[30px] font-normal hover:bg-transparent focus:bg-transparent disabled:cursor-not-allowed",
          className,
        )}
      >
        <div className="size-5">
          <RefreshIcon
            className={
              isExpired ? "text-icon-neutral-secondary" : "text-dark_black04"
            }
            width="16"
            height="16"
          />
        </div>

        {isLoading ? (
          <span className="text-text-nautral-secondary text-xs">
            در حال ارسال...
          </span>
        ) : isExpired ? (
          <span className="text-text-nautral-secondary text-xs">
            ارسال دوباره پیامک رمز
          </span>
        ) : (
          <>
            برای ارسال دوباره پیامک رمز
            <b className="text-dark_black6 min-h-[16px] !w-[27px] px-px text-center">
              {`${minutes < 10 ? "0" : ""}${minutes}:${seconds < 10 ? "0" : ""}${seconds}`}{" "}
            </b>{" "}
            صبر کنید.
          </>
        )}
      </button>
    </div>
  );
}

export default OtpTimer;
