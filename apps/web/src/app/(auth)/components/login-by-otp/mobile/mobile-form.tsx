import { ROUTE_LOGIN } from "@/constants/routes";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { Loading } from "@workspace/ui/components/loading";
import { useRouter } from "next/navigation";
import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import { useForm, UseFormSetError } from "react-hook-form";
import { z } from "zod";
import { CaptchaInput } from "@/components/captcha-input";
import {
  useCaptchaRequestTextCaptcha,
  useCaptchaWhichCaptchaIsActive,
} from "@workspace/investment-api/auto-generated/apis/captcha/captcha";
import { CaptchaInputRef } from "@/components/captcha-input/captcha-input";
import { CaptchaTypeEnum } from "@workspace/investment-api/auto-generated/models";
import { Input } from "@workspace/ui/components/input";
import { AuthTabbar } from "@/app/(auth)/components/auth-tabbar";
import { SplashLoading } from "@/components/splash-loading";
import { useLoginQueryStates } from "@/app/(auth)/hooks/useLoginQueryStates";

export type LoginByOtpFormValues = {
  mobileNumber?: string;
  secretKey: string;
  captchaCode?: string;
  captchaType?: number;
  otpExpirationDate?: string;
  otpSendDate?: string;
};

export type LoginByOtpHandleSubmitFunction = (
  values: LoginByOtpFormValues,
) => void;

interface LoginByOtpProps {
  handleSubmit: LoginByOtpHandleSubmitFunction;
  captchaType: number | undefined;
  setCaptchaType: Dispatch<SetStateAction<number | undefined>>;
}

function MobileForm({
  handleSubmit,
  setCaptchaType,
  captchaType,
}: LoginByOtpProps) {
  const router = useRouter();
  const inputRef = useRef<HTMLInputElement>(null);
  const captchaInputRef = useRef<CaptchaInputRef>(null);
  const [queryStates, setQueryStates] = useLoginQueryStates();

  const formSchema = z.object({
    mobileNumber: z
      .string()
      .regex(/^09\d{9}$/, "اطلاعات وارد شده صحیح نیست!")
      .nonempty("لطفا ابتدا فرم زیر را تکمیل کنید."),
    secretKey: z.string(),
    captchaCode:
      captchaType === CaptchaTypeEnum.GoogleRecaptchaV3
        ? z.string().optional()
        : z.string().nonempty("کد تصویری منقضی شده یا صحیح نیست!"),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      mobileNumber: "",
      secretKey: "",
      captchaCode: "",
    },
    mode: "onSubmit",
  });

  const secretKey = form.watch("secretKey");
  const captchaCode = form.watch("captchaCode");

  const [isLoading, setIsLoading] = useState(
    captchaType === CaptchaTypeEnum.GoogleRecaptchaV3 ? !secretKey : true,
  );

  function onSubmit(values: z.infer<typeof formSchema>) {
    inputRef.current?.blur();
    captchaInputRef.current?.refreshCaptcha();

    handleSubmit({
      ...values,
      captchaCode,
      captchaType,
    });
  }

  return (
    <div className="flex flex-1 flex-col">
      <div className="mt-13 flex flex-col gap-4">
        <h2 className="text-text-nautral-default text-lg font-bold">
          خوش آمدید.
        </h2>
        <AuthTabbar />
      </div>

      <div className="mt-5 flex flex-1 flex-col justify-between gap-6">
        <Form {...form}>
          <form
            id="login-form"
            onSubmit={form.handleSubmit(onSubmit)}
            className="mt-0.5 flex flex-col justify-between"
          >
            {isLoading && <SplashLoading />}
            <div className="flex flex-col justify-between">
              <FormField
                control={form.control}
                name="mobileNumber"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        size="lg"
                        title="شماره موبایل"
                        {...field}
                        ref={inputRef}
                        helperText={fieldState?.error?.message}
                        aria-invalid={!!fieldState?.error?.message}
                        data-test="dbb97343-4f3a-4c52-8327-5012050dc333"
                        onBlur={() => {
                          field.onBlur();
                          form.clearErrors("mobileNumber");
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="captchaCode"
                render={({ field, fieldState }) => (
                  <FormItem className="mt-3">
                    <FormControl>
                      <CaptchaInput
                        useCaptchaWhichCaptchaIsActive={
                          useCaptchaWhichCaptchaIsActive
                        }
                        useCaptchaRequestTextCaptcha={
                          useCaptchaRequestTextCaptcha
                        }
                        onCaptchaChange={({ secretKey, captchaCode }) => {
                          field.onChange(captchaCode);
                          form.setValue("secretKey", secretKey || "");
                        }}
                        googleRecaptchaSiteKey={
                          process.env.NEXT_PUBLIC_RECAPTCHA || ""
                        }
                        inputProps={{
                          title: "کد  را وارد کنید",
                          placeholder: " ",
                          "aria-invalid": !!fieldState?.error?.message,
                        }}
                        setIsLoading={setIsLoading}
                        ref={captchaInputRef}
                        setCaptchaType={setCaptchaType}
                        error={form?.formState?.errors?.captchaCode?.message}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
      </div>

      <div className="flex flex-col gap-3">
        <Button
          variant="noFrame"
          color="default"
          size="md"
          type="button"
          form="login-form"
          disabled={form.formState.isSubmitting}
          data-test="8c97c56e-fa65-4aff-83ed-0ccfed41778e"
          onClick={() => setQueryStates({ loginType: 100 })}
        >
          ورود با رمز عبور{" "}
        </Button>
        <Button
          variant="fill"
          size="md"
          type="submit"
          form="login-form"
          disabled={form.formState.isSubmitting}
          endAdornment={form.formState.isSubmitting && <Loading size="sm" />}
          data-test="602c0d8e-ff49-486c-9b5b-834e5280bac9"
        >
          دریافت رمز یکبارمصرف
        </Button>
      </div>
    </div>
  );
}
export default MobileForm;
