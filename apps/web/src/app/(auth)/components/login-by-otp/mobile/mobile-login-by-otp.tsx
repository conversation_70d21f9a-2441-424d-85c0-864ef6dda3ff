import { useOtpHook } from "@/app/(auth)/hooks/useOtpHook";
import { useEffect, useState } from "react";
import { UseFormSetError } from "react-hook-form";
import MobileForm, {
  LoginByOtpFormValues,
} from "@/app/(auth)/components/login-by-otp/mobile/mobile-form";
import MobileOtp, {
  MobileOtpFormValues,
} from "@/app/(auth)/components/login-by-otp/mobile/mobile-otp";
import {
  useAuthGetLoginOTPSecretKey,
  useAuthLoginByOTP,
} from "@workspace/investment-api/auto-generated/apis/auth/auth";
import { CaptchaTypeEnum } from "@workspace/investment-api/auto-generated/models";
import { useLoginQueryStates } from "@/app/(auth)/hooks/useLoginQueryStates";

export type LoginItpFormValues = {
  mobileNumber: string;
  secretKey?: string;
  otp: string;
};

export type SetErrorLoginByOtp = UseFormSetError<
  Omit<LoginItpFormValues, "secretKey">
>;

export type LoginByOtpHandleSubmitFunction = (
  value: MobileOtpFormValues,
  { setError }: { setError: SetErrorLoginByOtp },
) => Promise<unknown>;

interface LoginByOtpProps {
  handleSubmit: LoginByOtpHandleSubmitFunction;
}

function MobileLoginByOtp({ handleSubmit }: LoginByOtpProps) {
  const [loginOtpValue, setLoginOtpValue] = useState<
    LoginByOtpFormValues | undefined
  >(undefined);

  const [captchaType, setCaptchaType] = useState<undefined | number>(undefined);

  const loginMethodHeaders =
    captchaType && captchaType === CaptchaTypeEnum.GoogleRecaptchaV3
      ? {
          "captcha-code": loginOtpValue?.secretKey,
          "Content-Type": "multipart/form-data",
        }
      : {
          "captcha-code": loginOtpValue?.captchaCode,
          "captcha-secret-key": loginOtpValue?.secretKey,
          "Content-Type": "multipart/form-data",
        };

  const { data: secretKeyData, refetch: refetchSecretKey } =
    useAuthGetLoginOTPSecretKey(loginOtpValue?.mobileNumber ?? "", {
      request: {
        headers: loginMethodHeaders,
      },
      query: {
        enabled: false,
      },
    });

  const { isLoadingSendOtp, onSendOtp } = useOtpHook();

  const handleSendOtp = () => {
    return onSendOtp({ sk: secretKeyData?.data }).then((res) => {
      setLoginOtpValue((prev) => {
        if (!prev) return prev;
        return {
          ...prev,
          otpExpirationDate: res?.data?.otpExpirationDate,
          otpSendDate: res?.data?.otpSendDate,
        };
      });
      return res;
    });
  };

  const handleSubmitMobileForm = (value: LoginByOtpFormValues) => {
    setLoginOtpValue({
      ...value,
    });
    return new Promise((resolve, reject) => {
      return setTimeout(async () => {
        return await refetchSecretKey().then((refetchRes) => {
          onSendOtp({ sk: refetchRes?.data?.data })
            .then((res) => {
              resolve(res);
              setLoginOtpValue({
                ...value,
                secretKey: refetchRes?.data?.data ?? "",
                otpExpirationDate: res?.data?.otpExpirationDate,
                otpSendDate: res?.data?.otpSendDate,
              });
            })
            .catch((err) => {
              reject(err);
            });
        });
      }, 100);
    });
  };

  if (loginOtpValue?.otpExpirationDate) {
    return (
      <MobileOtp
        {...{
          onSendOtp: handleSendOtp,
          mobile: loginOtpValue?.mobileNumber,
          secretKey: loginOtpValue?.secretKey,
          isLoadingSendOtp,
          otpSendDate: loginOtpValue?.otpSendDate || "",
          otpExpirationDate: loginOtpValue?.otpExpirationDate,
        }}
        handleSubmit={(otpValue, { setError }) =>
          handleSubmit(
            {
              mobileNumber: otpValue?.mobileNumber,
              otp: otpValue?.otp,
              secretKey: loginOtpValue?.secretKey,
            },
            { setError },
          )
        }
      />
    );
  }

  return (
    <MobileForm
      handleSubmit={(value) => handleSubmitMobileForm(value)}
      captchaType={captchaType}
      setCaptchaType={setCaptchaType}
    />
  );
}
export default MobileLoginByOtp;
