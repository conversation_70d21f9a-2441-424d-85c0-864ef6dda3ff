import OtpTimer from "@/app/(auth)/components/login-by-otp/otp-timer";
import { OTPHookReturnType, useOtpHook } from "@/app/(auth)/hooks/useOtpHook";
import BackIcon from "@/assets/icons/arrow-right.svg";
import { ROUTE_LOGIN } from "@/constants/routes";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { InputNumber } from "@workspace/ui/components/input-number";
import { Loading } from "@workspace/ui/components/loading";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useRef } from "react";
import { useForm, UseFormSetError } from "react-hook-form";
import { z } from "zod";
import { WRONG_CODE_MESSAGE } from "@/app/(auth)/utils";
import { LoginByOtpHandleSubmitFunction } from "@/app/(auth)/components/login-by-otp/mobile/mobile-login-by-otp";
import { useLoginQueryStates } from "@/app/(auth)/hooks/useLoginQueryStates";

const formSchema = z.object({
  mobileNumber: z.string().nonempty("لطفا ابتدا فرم زیر را تکمیل کنید."),
  otp: z.string().nonempty("لطفا ابتدا فرم زیر را تکمیل کنید."),
});

export type MobileOtpFormValues = z.infer<typeof formSchema> & {
  secretKey: string;
};

export type SetErrorLoginByOtp = UseFormSetError<z.infer<typeof formSchema>>;

interface LoginByOtpProps extends OTPHookReturnType {
  handleSubmit: LoginByOtpHandleSubmitFunction;
  secretKey?: string;
  mobile?: string;
}

const MAX_OTP_NUMBER = 6;

function MobileOtp({
  secretKey,
  handleSubmit,
  mobile,
  isLoadingSendOtp,
  onSendOtp,
  otpExpirationDate,
  otpSendDate,
}: LoginByOtpProps) {
  const router = useRouter();
  const inputRef = useRef<HTMLInputElement>(null);
  const [queryStates, setQueryStates] = useLoginQueryStates();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      mobileNumber: "",
      otp: "",
    },
    mode: "onSubmit",
  });

  const otp = form.watch("otp");

  function onSubmit(values: z.infer<typeof formSchema>) {
    inputRef.current?.blur();

    return handleSubmit(
      { ...values, secretKey: secretKey || "" },
      { setError: form.setError },
    );
  }

  useEffect(() => {
    if (!mobile) {
      router.replace(ROUTE_LOGIN);
    }
  }, [mobile]);

  useEffect(() => {
    if (otp.length === MAX_OTP_NUMBER) {
      onSubmit({ otp, mobileNumber: mobile! });
    }
  }, [otp]);

  /* ------------------------------- auto login ------------------------------- */
  useEffect(() => {
    if ("OTPCredential" in window) {
      const ac = new AbortController();

      navigator.credentials
        .get({
          otp: { transport: ["sms"] },
          signal: ac.signal,
        } as any)
        .then((otpCredential: any) => {
          if (otpCredential && otpCredential?.code) {
            form.setValue("otp", otpCredential?.code);
          }
        })
        .catch((err) => {
          console.error("WebOTP error:", err);
        });

      return () => ac.abort();
    }
  }, []);

  return (
    <div className="mt-6 flex flex-1 flex-col justify-between">
      <div className="flex flex-col justify-between gap-1">
        <div className="mb-2">
          <div
            className="flex cursor-pointer items-center gap-1"
            onClick={() => window.location.reload()}
            data-test="294b2833-5772-4393-bea9-6eb46fd50e8a"
          >
            <BackIcon className="text-text-nautral-default size-4" />
            <span className="text-text-nautral-default text-xs">
              ویرایش شماره
            </span>
          </div>

          <p className="text-text-nautral-default mt-3 text-lg font-bold">
            ورود{" "}
          </p>
          <p className="text-text-nautral-secondary mt-4 flex items-center text-xs">
            کد پیامک شده به شماره
            <span className="text-text-nautral-default mx-0.5">{mobile}</span>
            را وارد کنید.
          </p>
        </div>

        <Form {...form}>
          <form
            id="login-form"
            onSubmit={form.handleSubmit(onSubmit)}
            className="mt-0.5 mb-5 flex flex-col justify-between"
          >
            <div className="flex flex-col justify-between">
              <FormField
                control={form.control}
                name="otp"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <InputNumber
                        size="lg"
                        commaSeparated={false}
                        {...field}
                        placeholder="------"
                        autoFocus
                        maxLength={MAX_OTP_NUMBER}
                        ref={inputRef}
                        onChange={(e) => {
                          field.onChange(e);
                          if (
                            fieldState?.error?.message === WRONG_CODE_MESSAGE
                          ) {
                            form.clearErrors("otp");
                          }
                        }}
                        onBlur={() => {
                          field.onBlur();
                          form.clearErrors("otp");
                        }}
                        rootClassName="!ps-2 !pe-5"
                        className="w-full text-center tracking-[14px] placeholder:text-center placeholder:tracking-[14px]"
                        dir="ltr"
                        helperText={
                          fieldState?.error?.message !== WRONG_CODE_MESSAGE
                            ? fieldState?.error?.message
                            : ""
                        }
                        aria-invalid={!!fieldState?.error?.message}
                        data-test="271a1882-cb0b-4d9b-98d5-172f69cf86d3"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>

        <OtpTimer
          expirationDate={otpExpirationDate || ""}
          otpSendDate={otpSendDate || ""}
          onResend={() => onSendOtp({})}
          isLoading={isLoadingSendOtp}
        />
      </div>

      <div className="flex flex-col gap-3">
        <Button
          variant="noFrame"
          color="default"
          size="md"
          type="button"
          form="login-form"
          disabled={form.formState.isSubmitting}
          data-test="8c97c56e-fa65-4aff-83ed-0ccfed41778e"
          onClick={() => setQueryStates({ loginType: 100 })}
        >
          ورود با رمز عبور{" "}
        </Button>
        <Button
          variant="fill"
          size="md"
          type="submit"
          form="login-form"
          disabled={form.formState.isSubmitting}
          endAdornment={form.formState.isSubmitting && <Loading size="sm" />}
          data-test="602c0d8e-ff49-486c-9b5b-834e5280bac9"
        >
          ورود
        </Button>
      </div>
    </div>
  );
}
export default MobileOtp;
