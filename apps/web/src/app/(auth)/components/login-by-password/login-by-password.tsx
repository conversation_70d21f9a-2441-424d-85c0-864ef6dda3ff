import { ResponsiveRenderer } from "@workspace/ui/components/responsive-renderer";
import MobileLoginByPassword, {
  LoginByPasswordFormValues,
  LoginByPasswordHandleSubmitFunction,
} from "@/app/(auth)/components/login-by-password/mobile/mobile-login-by-password";

export type { LoginByPasswordFormValues, LoginByPasswordHandleSubmitFunction };

interface LoginByPasswordProps {
  handleSubmitPassword: LoginByPasswordHandleSubmitFunction;
}

function LoginByPassword({ handleSubmitPassword }: LoginByPasswordProps) {
  return (
    <ResponsiveRenderer
      desktop={<MobileLoginByPassword handleSubmit={handleSubmitPassword} />}
      mobile={<MobileLoginByPassword handleSubmit={handleSubmitPassword} />}
    />
  );
}
export default LoginByPassword;
