import ArrowRight from "@/assets/icons/arrow-right.svg";
import Header from "@/components/header";
import { ROUTE_HOME } from "@/constants/routes";
import { useRouter } from "next/navigation";
import { ReactNode } from "react";

interface IMobileLayout {
  children: ReactNode;
  title?: string;
}

function MobileLayout({ children, title }: IMobileLayout) {
  const router = useRouter();

  const handleClick = () => {
    router.push(ROUTE_HOME);
  };

  return (
    <div className="flex h-full flex-col">
      <Header
        title={title}
        titleClassName="font-normal"
        center
        startAdornment={
          <div
            className="bg-surface-nautral-default-3 shrink-0 cursor-pointer rounded-sm p-2"
            onClick={handleClick}
          >
            <ArrowRight className="text-icon-primary-default size-4" />
          </div>
        }
      />
      {children}
    </div>
  );
}

export default MobileLayout;
