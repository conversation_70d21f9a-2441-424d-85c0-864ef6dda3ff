import <PERSON>ba<PERSON>ogo from "@/assets/icons/saba-login-logo.svg";
import { ReactNode } from "react";

interface IMobileLayout {
  children: ReactNode;
}

function MobileLayout(props: IMobileLayout) {
  const { children } = props;

  return (
    <div className="bg-surface-nautral-default-4 h-full px-4 py-4.5">
      <div className="flex h-full flex-col">
        <SabaLogo className="text-icon-neutral-default mx-auto h-[56px] w-[288px]" />
        {children}
      </div>
    </div>
  );
}

export default MobileLayout;
