import SwitchTabs from "@workspace/ui/components/switchTabs";
import { usePathname, useRouter } from "next/navigation";
import { useState } from "react";

const authTabs = [
  {
    id: "login",
    label: "ورود",
  },
  {
    id: "register",
    label: "ثبت‌نام",
  },
];

export function AuthTabbar() {
  const router = useRouter();
  const pathname = usePathname();
  const isActive =
    authTabs.find((tab) => pathname.includes(tab.id))?.id || authTabs.at(0)?.id;

  const [internalActiveTab, setInternalActiveTab] = useState(isActive);

  const handleTabChange = (tab: string | number) => {
    setInternalActiveTab(tab as string);
    router.push(`/${tab}`);
  };

  return (
    <SwitchTabs
      tabs={authTabs}
      activeItemId={internalActiveTab}
      onChange={handleTabChange}
    />
  );
}
