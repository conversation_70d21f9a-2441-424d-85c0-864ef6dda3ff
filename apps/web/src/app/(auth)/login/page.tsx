"use client";

import { useState, Suspense } from "react";
import { toast } from "@workspace/ui/components/toast";
import { useRouter } from "next/navigation";
import { ROUTE_LOGIN } from "@/constants/routes";
import LoginByPassword, {
  LoginByPasswordFormValues,
} from "@/app/(auth)/components/login-by-password/login-by-password";
import { SetErrorLoginByOtp } from "@/app/(auth)/components/login-by-otp/mobile/mobile-login-by-otp";
import LoginByOtp from "@/app/(auth)/components/login-by-otp/login-by-otp";
import { useLoginQueryStates } from "@/app/(auth)/hooks/useLoginQueryStates";
import {
  THREE_TIMES_TRY_ERROR,
  WRONG_CAPTCHA_CODE,
  WRONG_CODE_MESSAGE,
  WRONG_OTP_ERROR,
} from "@/app/(auth)/utils";
import { useLogin } from "@/hooks/useLogin";
import {
  useAuthLoginByOTP,
  useAuthLoginByPassword,
} from "@workspace/investment-api/auto-generated/apis/auth/auth";
import { MobileOtpFormValues } from "@/app/(auth)/components/login-by-otp/mobile/mobile-otp";

function LoginLoading() {
  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
    </div>
  );
}

function LoginContent() {
  const router = useRouter();
  const [secretKey, setSecretKey] = useState("");
  const [captchaCode, setCaptchaCode] = useState("");
  const { login } = useLogin();

  const [queryStates, setQueryStates] = useLoginQueryStates();

  const loginType = queryStates?.loginType;

  const errorTile = (errorCode?: string) =>
    errorCode && THREE_TIMES_TRY_ERROR.includes(errorCode)
      ? "حساب شما مسدود است!"
      : " ";

  const loginMethodHeaders = {
    "captcha-code": captchaCode,
    "captcha-secret-key": secretKey,
    "Content-Type": "multipart/form-data",
  };

  const { mutateAsync: loginByPassword } = useAuthLoginByPassword({
    request: {
      headers: loginMethodHeaders,
    },
  });
  const { mutateAsync: loginByOtp } = useAuthLoginByOTP();

  // /* ------------------------- handle submit password ------------------------- */
  const handleSubmitPassword = async (value: LoginByPasswordFormValues) => {
    setCaptchaCode(value?.captchaCode);
    setSecretKey(value?.secretKey || "");
    return new Promise((resolve, reject) => {
      return setTimeout(async () => {
        return await loginByPassword({
          data: {
            nationalCodeOrMobileNumber: value?.nationalCodeOrMobileNumber,
            password: value?.password,
          },
        })
          .then((res) => {
            login();
            resolve(res);
          })
          .catch((err) => {
            reject(err);

            if (err.response?.data?.errorMessage) {
              const hasErrorTitle = errorTile(err?.response?.data?.errorCode);
              toast.error(
                hasErrorTitle
                  ? errorTile(err?.response?.data?.errorCode)
                  : err?.response?.data?.errorMessage,
                {
                  description: hasErrorTitle
                    ? err?.response?.data?.errorMessage
                    : undefined,
                },
              );
            }
          });
      }, 100);
    });
  };

  // /* ---------------------------- handle submit otp --------------------------- */
  const handleSubmitOtp = async (
    value: MobileOtpFormValues,
    { setError }: { setError: SetErrorLoginByOtp },
  ) => {
    setCaptchaCode(value?.secretKey);

    return new Promise((resolve, reject) => {
      return setTimeout(async () => {
        return await loginByOtp({
          data: { otp: value?.otp, secretKey: value?.secretKey },
        })
          .then((res) => {
            login();
            resolve(res);
          })
          .catch((err) => {
            reject(err);
            if (err.response?.data?.errorCode === WRONG_OTP_ERROR) {
              setError("otp", { message: WRONG_CODE_MESSAGE });

              toast.error(err.response?.data?.errorMessage);
            } else if (err.response?.data?.errorMessage) {
              const hasErrorTitle = errorTile(err?.response?.data?.errorCode);
              toast.error(
                hasErrorTitle
                  ? errorTile(err?.response?.data?.errorCode)
                  : err?.response?.data?.errorMessage,
                {
                  description: hasErrorTitle
                    ? err?.response?.data?.errorMessage
                    : undefined,
                },
              );
              if (
                [...THREE_TIMES_TRY_ERROR, WRONG_CAPTCHA_CODE].includes(
                  err.response?.data?.errorCode,
                )
              ) {
                window.location.reload();
              }
            }
          });
      }, 100);
    });
  };

  switch (loginType) {
    case 100:
      return <LoginByPassword handleSubmitPassword={handleSubmitPassword} />;

    case 200:
      return <LoginByOtp handleSubmitOtp={handleSubmitOtp} />;

    default:
      return <LoginByPassword handleSubmitPassword={handleSubmitPassword} />;
  }
}

export default function Page() {
  return (
    <Suspense fallback={<LoginLoading />}>
      <LoginContent />
    </Suspense>
  );
}
