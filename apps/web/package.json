{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack -p 3000", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-progress": "^1.1.7", "@tailwindcss/postcss": "^4.0.8", "@tanstack/react-query": "^5.79.0", "@workspace/investment-api": "workspace:*", "@workspace/ui": "workspace:*", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "date-fns": "^4.1.0", "highcharts": "^12.2.0", "highcharts-react-official": "^3.2.2", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "lucide-react": "^0.475.0", "next": "^15.2.3", "next-themes": "^0.4.4", "nuqs": "^2.4.3", "path-to-regexp": "^6.2.1", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-google-recaptcha-v3": "^1.11.0", "react-hook-form": "^7.57.0", "sass": "^1.89.2", "swiper": "^11.2.10", "tailwindcss": "^4.0.8", "zod": "^3.25.67"}, "devDependencies": {"@jest/globals": "^29.7.0", "@storybook/react": "^8.6.14", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier-plugin-tailwindcss": "^0.6.11", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}